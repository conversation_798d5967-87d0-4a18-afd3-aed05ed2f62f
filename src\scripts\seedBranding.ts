import mongoose from 'mongoose';
import { Branding } from '../models';
import dotenv from 'dotenv';

dotenv.config();

const defaultBrandingData = {
  global: {
    siteName: 'DammySpicy Beauty LLC',
    tagline: 'Where Beauty Meets Intention',
    logo: '',
    favicon: '',
    phone: '(*************',
    email: '<EMAIL>',
    address: 'Indianapolis, IN',
    instagram: '',
    facebook: '',
    twitter: '',
    youtube: ''
  },
  home: {
    heroTitle: 'dammyspicybeauty',
    heroSubtitle: 'Professional Hair Care Services',
    heroImage: '',
    businessName: 'dammyspicybeauty',
    location: 'INDIANAPOLIS, IN',
    welcomeText: 'Welcome to my booking site!',
    aboutTitle: 'About Me',
    aboutText: `<p>Thank you for choosing Dammyspicy Beauty.</p>
<p>My name is <PERSON><PERSON>, and I am a licensed cosmetologist specializing in hair care and beauty treatments for natural hair, including microlocs and more. Based in Indianapolis, IN, my passion is helping women embrace their natural beauty with confidence.</p>
<p>My main objective is to bring out the beauty in each individual, put smiles on faces, and create styles that reflect uniqueness and elegance.</p>
<p>I'm excited to begin this healthy hair journey with you!</p>`,
    featuredServices: [],
    testimonialHeading: 'What Our Clients Say',
    policies: {
      title: 'Policies & Terms of Service',
      companyName: 'DammySpicy Beauty LLC',
      tagline: 'Where Beauty Meets Intention',
      sections: [
        {
          icon: '📌',
          title: 'TO BOOK',
          content: 'A non-refundable deposit is required to secure any service. This amount goes toward your total service.'
        },
        {
          icon: '⏰',
          title: 'REMAINING BALANCE',
          content: 'CASH ONLY is accepted at time of your appointment. No digital payments will be accepted'
        },
        {
          icon: '🚫',
          title: 'RESCHEDULING',
          content: 'Same-day reschedule - loss of deposit'
        },
        {
          icon: '❌',
          title: 'CANCELLATIONS',
          content: 'All services made at least 24 hours before your appointment.'
        },
        {
          icon: '🚫',
          title: 'NO REFUNDS',
          content: 'All services are final once completed.'
        }
      ],
      paymentOptions: {
        cashApp: {
          number: '(*************'
        },
        zelle: {
          name: 'DammySpicyBeauty LLC',
          number: '(*************'
        }
      },
      footerText: 'Thank you for choosing DammySpicy Beauty LLC. We appreciate your business and look forward to serving you with love and excellence!'
    },
    disclaimer: {
      title: 'Disclaimer',
      content: `<p>At DammySpicy Beauty, we are committed to delivering the best possible service tailored to each individual. However, results may vary from person to person due to differences in hair type, density, texture, lifestyle, and aftercare. While we strive for excellence, we cannot guarantee identical outcomes for everyone.</p>
<p>By booking and receiving services, you acknowledge that all hairstyling and haircare services are provided at your request. The service provider and its stylists are not responsible for dissatisfaction, perceived damages, or outcomes outside of what was discussed and agreed upon prior to service. By proceeding with an appointment, you waive the right to pursue legal action against the service provider or its affiliates in connection with services rendered.</p>
<p>All services, payments, and sales are final once completed.</p>
<p>Please note that photos and videos may be captured and shared on our social media platforms for promotional purposes, unless you explicitly request otherwise before your appointment.</p>`
    },
    microlocks: {
      title: 'Microlocks',
      content: '<p>Full payment is required to secure all Microlock establishment appointments. This must be paid at the time of scheduling.</p>',
      methodsTitle: '3 STARTING METHODS TO CHOOSE FROM:',
      methods: [
        '1 - INTERLOCKS',
        '2 - TWO STRAND TWIST',
        '3 - BRAID LOCS'
      ],
      durationText: 'Establishment usually take 10-12 hours or more depending on Hair, Lock size, Lock Method, Hair Length, Density and Head size'
    },
    consultation: {
      subtitle: 'READY TO START YOUR LOC JOURNEY?',
      intro: 'To begin, you\'ll need to book a consultation which costs $30 (non-refundable).',
      optionsTitle: 'We offer two options to suit your needs:',
      options: [
        { title: '1. IN-PERSON CONSULTATION' },
        { title: '2. VIDEO CALL CONSULTATION' }
      ],
      duration: 'Each session will be 30 minutes',
      benefits: [
        '✓ Curating your hair goals',
        '✓ Discussing your hair type and its condition',
        '✓ Recommending the best methods for your loc journey to thrive'
      ],
      note: 'Keep in mind, this consultation is mandatory for better understanding your hair journey.',
      ctaText: 'BOOK YOUR CONSULTATION NOW',
      readMoreText: 'Read More',
      readLessText: 'Read Less',
      preparation: {
        title: 'HOW TO BE PREPARED FOR YOUR CONSULTATION',
        items: [
          'Hair Must Be Undone - Please ensure your hair is completely taken down (no braids, twists, or extensions)',
          'Clean & Product-Free - Wash your hair and avoid using oils, creams, or gels',
          'Natural State - Your hair should be free from heat styling and in its natural texture',
          'Good Lighting - Be in a well-lit area so your hair can be seen clearly',
          'Clear Video or Photos - If the consultation is virtual, send a clear video or pictures of your hair before the appointment',
          'Questions Ready - Write down any questions or concerns you may have about the service'
        ]
      },
      installation: {
        title: 'Things to Do Before Your Installation Appointment',
        items: [
          'Wash your hair - the day before or the morning of your appointment',
          'Skip blowouts, flat irons, or curling irons',
          'Detangle gently - Make sure your hair is free from knots',
          'Avoid products - avoid gels, oils, or creams in your hair',
          'Plan for comfort - Bring your headphones, snacks, laptop, or a drink - appointments take time!',
          'Arrive on time - Being on time helps us start and finish smoothly'
        ]
      },
      transfer: {
        title: 'CONSULTATION FOR TRANSFERRED CLIENTS',
        content: 'A consultation is required for all transferred clients.',
        fee: 'The fee is $30 (non-refundable)',
        description: 'This process allows us to better understand your loc journey and guide you through the next steps for a smooth, continuous experience.'
      }
    }
  }
};

async function seedBranding() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/microlocs');
    console.log('Connected to MongoDB');

    // Check if branding already exists
    const existingBranding = await Branding.findOne();
    
    if (existingBranding) {
      console.log('Branding data already exists. Updating with new structure...');
      await Branding.findByIdAndUpdate(existingBranding._id, defaultBrandingData, { new: true });
      console.log('Branding data updated successfully');
    } else {
      console.log('Creating new branding data...');
      await Branding.create(defaultBrandingData);
      console.log('Branding data created successfully');
    }

    console.log('Branding seed completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error seeding branding data:', error);
    process.exit(1);
  }
}

// Run the seed function
seedBranding();
