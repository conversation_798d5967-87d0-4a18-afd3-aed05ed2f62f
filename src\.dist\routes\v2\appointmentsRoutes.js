"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const date_fns_1 = require("date-fns");
const models_1 = require("../../models");
const auth_1 = require("../../middleware/auth");
const validation_1 = require("../../middleware/validation");
const validation_2 = require("../../utils/validation");
const response_1 = require("../../utils/response");
const controllers_1 = require("../../controllers");
const availabilityService_1 = require("../../services/availabilityService");
const router = (0, express_1.Router)();
// GET /api/v2/appointments - Get all appointments (admin only)
router.get('/', auth_1.authenticate, (0, auth_1.authorize)('admin'), (0, validation_1.validate)(validation_2.paginationValidation), async (req, res) => {
    try {
        const { status, date, page = 1, limit = 20, search } = req.query;
        const filter = {};
        if (status) {
            filter.status = status;
        }
        if (date) {
            const searchDate = new Date(date);
            filter.date = {
                $gte: searchDate,
                $lt: new Date(searchDate.getTime() + 24 * 60 * 60 * 1000)
            };
        }
        if (search) {
            filter.$or = [
                { 'customerInfo.name': { $regex: search, $options: 'i' } },
                { 'customerInfo.email': { $regex: search, $options: 'i' } },
                { 'customerInfo.phone': { $regex: search, $options: 'i' } }
            ];
        }
        const pageNum = Number(page);
        const limitNum = Number(limit);
        const skip = (pageNum - 1) * limitNum;
        const [appointments, total] = await Promise.all([
            models_1.Appointment.find(filter)
                .populate('user', 'name email phone')
                .populate('service', 'name duration price category')
                .sort({ date: -1, time: -1 })
                .skip(skip)
                .limit(limitNum),
            models_1.Appointment.countDocuments(filter)
        ]);
        const totalPages = Math.ceil(total / limitNum);
        // Format appointments for v2 response
        const formattedAppointments = appointments.map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service?._id,
            serviceName: appointment.service?.name,
            servicePrice: appointment.service?.price,
            serviceDuration: appointment.service?.duration,
            serviceCategory: appointment.service?.category,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            user: appointment.user ? {
                id: appointment.user._id,
                name: appointment.user.name,
                email: appointment.user.email,
                phone: appointment.user.phone
            } : null,
            notes: appointment.message,
            createdAt: appointment.createdAt,
            updatedAt: appointment.updatedAt
        }));
        (0, response_1.sendSuccess)(res, 'Appointments retrieved successfully', {
            appointments: formattedAppointments,
            pagination: {
                currentPage: pageNum,
                totalPages,
                totalItems: total,
                itemsPerPage: limitNum,
                hasNextPage: pageNum < totalPages,
                hasPrevPage: pageNum > 1
            }
        });
    }
    catch (error) {
        console.error('Get all appointments error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// Note: Availability checking has been moved to /api/v2/availability endpoints
// This endpoint is deprecated and will be removed in a future version
// Note: Availability endpoints have been moved to /api/v2/availability
// This endpoint is deprecated and will be removed in a future version
// POST /api/v2/appointments - Create new appointment (public endpoint)
router.post('/', async (req, res) => {
    try {
        const { serviceId, date, time, customerInfo, addOns = [], userId, paymentProof } = req.body;
        // Validate required fields including payment proof
        if (!serviceId || !date || !time || !customerInfo) {
            (0, response_1.sendError)(res, 'Service, date, time, and customer info are required', undefined, 400);
            return;
        }
        // Payment proof is required
        if (!paymentProof || !paymentProof.url) {
            (0, response_1.sendError)(res, 'Payment proof is required to create an appointment', undefined, 400);
            return;
        }
        // Decode time if it's URL encoded
        const decodedTime = decodeURIComponent(time).replace(':undefined', '');
        // Find or create user based on email
        let user = null;
        if (userId) {
            user = await models_1.User.findById(userId);
        }
        else if (customerInfo.email) {
            // Check if user exists by email
            user = await models_1.User.findOne({ email: customerInfo.email.toLowerCase() });
            // If user doesn't exist, create one automatically without password
            if (!user) {
                try {
                    // Create user without bcrypt - they can set password later if needed
                    user = await models_1.User.create({
                        name: `${customerInfo.firstName} ${customerInfo.lastName}`,
                        firstName: customerInfo.firstName,
                        lastName: customerInfo.lastName,
                        email: customerInfo.email.toLowerCase(),
                        phone: customerInfo.phone || '',
                        role: 'user',
                        isVerified: false,
                        // Use a placeholder password that will require reset
                        password: 'TEMP_PASSWORD_REQUIRES_RESET_' + Date.now()
                    });
                    console.log(`Auto-created user for email: ${customerInfo.email}`);
                }
                catch (createError) {
                    console.error('Error creating user:', createError);
                    // Continue without user if creation fails
                    user = null;
                }
            }
        }
        // Check if service exists
        const service = await models_1.Service.findById(serviceId);
        if (!service || !service.isActive) {
            (0, response_1.sendError)(res, 'Service not found or not available', undefined, 404);
            return;
        }
        // Parse the date and combine with time for a complete datetime
        // Use UTC to avoid timezone issues
        const appointmentDate = new Date(date + 'T00:00:00.000Z');
        // If time is provided, try to parse and combine with date
        let combinedDateTime = appointmentDate;
        if (decodedTime) {
            try {
                // Parse time (handle various formats)
                const timeMatch = decodedTime.match(/(\d{1,2}):(\d{2})\s*(AM|PM)?/i);
                if (timeMatch) {
                    let hours = parseInt(timeMatch[1]);
                    const minutes = parseInt(timeMatch[2]);
                    const period = timeMatch[3];
                    // Convert to 24-hour format if needed
                    if (period) {
                        if (period.toUpperCase() === 'PM' && hours !== 12) {
                            hours += 12;
                        }
                        else if (period.toUpperCase() === 'AM' && hours === 12) {
                            hours = 0;
                        }
                    }
                    // Set the time on the date using UTC
                    combinedDateTime = new Date(appointmentDate);
                    combinedDateTime.setUTCHours(hours, minutes, 0, 0);
                }
            }
            catch (timeError) {
                console.error('Error parsing time:', timeError);
                // Use original date if time parsing fails
            }
        }
        // Check admin availability using the availability service
        const isAvailable = await availabilityService_1.availabilityService.isTimeSlotAvailable((0, date_fns_1.format)(appointmentDate, 'yyyy-MM-dd'), decodedTime);
        if (!isAvailable) {
            (0, response_1.sendError)(res, `The selected time slot (${decodedTime}) is not available. Please choose another time.`, undefined, 409);
            return;
        }
        // Additional check: Verify no existing appointment at this exact time slot
        const existingAppointment = await models_1.Appointment.findOne({
            date: appointmentDate,
            time: decodedTime,
            status: { $in: ['pending', 'confirmed'] }
        });
        if (existingAppointment) {
            (0, response_1.sendError)(res, 'This time slot is already booked. Please select a different time.', undefined, 409);
            return;
        }
        // Calculate total price (for response only, not stored in appointment)
        let totalPrice = service.price;
        const addOnTotal = addOns.reduce((sum, addOn) => sum + (addOn.price || 0), 0);
        totalPrice += addOnTotal;
        // Prepare appointment data
        const appointmentData = {
            user: user ? user._id : null, // Use found/created user or null for guest bookings
            service: service._id,
            date: combinedDateTime, // Use combined date and time
            time: decodedTime,
            status: 'pending',
            type: 'service',
            customerInfo: {
                name: `${customerInfo.firstName} ${customerInfo.lastName}`,
                email: customerInfo.email,
                phone: customerInfo.phone
            },
            message: req.body.notes || '',
            totalPrice: totalPrice
        };
        // Add payment proof (required)
        console.log('Adding payment proof to appointment:', paymentProof);
        appointmentData.paymentProofs = [{
                id: new Date().getTime().toString(),
                amount: paymentProof.amount || totalPrice,
                paymentMethod: paymentProof.paymentMethod || 'unknown',
                proofImage: paymentProof.url,
                status: 'pending',
                notes: paymentProof.notes || 'Payment proof uploaded during booking',
                createdAt: new Date()
            }];
        appointmentData.paymentStatus = 'pending';
        console.log('Payment proof added to appointment data');
        // Create appointment with error handling for duplicates
        try {
            const appointment = await models_1.Appointment.create(appointmentData);
            // Populate service data for response
            await appointment.populate('service', 'name price duration category');
            const responseData = {
                id: appointment._id,
                userId: user ? user._id : null,
                serviceId: appointment.service._id,
                serviceName: appointment.service.name,
                servicePrice: appointment.service.price,
                date: appointment.date,
                time: appointment.time,
                status: appointment.status,
                customerInfo: appointment.customerInfo,
                totalPrice: totalPrice,
                addOns: addOns,
                notes: appointment.message,
                paymentProofs: appointment.paymentProofs || [],
                paymentStatus: appointment.paymentStatus || 'pending',
                createdAt: appointment.createdAt
            };
            (0, response_1.sendCreated)(res, 'Appointment created successfully', responseData);
        }
        catch (createError) {
            // Handle duplicate key error specifically
            if (createError.code === 11000 && createError.message.includes('date_1_time_1_status_1')) {
                (0, response_1.sendError)(res, 'Time slot is already booked. Please select a different time.', undefined, 409);
                return;
            }
            throw createError; // Re-throw if it's a different error
        }
    }
    catch (error) {
        console.error('Create appointment error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/appointments/user - Get user dashboard data (public endpoint)
router.get('/user', async (req, res) => {
    try {
        const { email, userId } = req.query;
        if (!email && !userId) {
            (0, response_1.sendError)(res, 'Email or userId query parameter is required. Usage: ?email=<EMAIL> or ?userId=123', undefined, 400);
            return;
        }
        // Find user by email or userId
        let user = null;
        if (userId) {
            user = await models_1.User.findById(userId);
        }
        else if (email) {
            user = await models_1.User.findOne({ email: email.toString().toLowerCase() });
        }
        if (!user) {
            (0, response_1.sendError)(res, 'User not found', undefined, 404);
            return;
        }
        // Get all user appointments with full population
        const appointments = await models_1.Appointment.find({ user: user._id })
            .populate('service', 'name duration price category description')
            .populate('user', 'name email phone')
            .sort({ date: -1, time: -1 });
        // Get appointment statistics
        const appointmentStats = {
            total: appointments.length,
            pending: appointments.filter(apt => apt.status === 'pending').length,
            confirmed: appointments.filter(apt => apt.status === 'confirmed').length,
            completed: appointments.filter(apt => apt.status === 'completed').length,
            cancelled: appointments.filter(apt => apt.status === 'cancelled').length
        };
        // Get recent appointments (last 5)
        const recentAppointments = appointments.slice(0, 5).map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service?._id,
            serviceName: appointment.service?.name,
            servicePrice: appointment.service?.price,
            serviceDuration: appointment.service?.duration,
            serviceCategory: appointment.service?.category,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            notes: appointment.message,
            createdAt: appointment.createdAt,
            updatedAt: appointment.updatedAt
        }));
        // Get upcoming appointments
        const now = new Date();
        const upcomingAppointments = appointments
            .filter(apt => {
            const appointmentDate = new Date(apt.date);
            return appointmentDate >= now && (apt.status === 'pending' || apt.status === 'confirmed');
        })
            .slice(0, 3)
            .map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service?._id,
            serviceName: appointment.service?.name,
            servicePrice: appointment.service?.price,
            serviceDuration: appointment.service?.duration,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            notes: appointment.message
        }));
        // Calculate total spent
        const totalSpent = appointments
            .filter(apt => apt.status === 'completed')
            .reduce((sum, apt) => {
            const servicePrice = apt.service?.price || 0;
            return sum + servicePrice;
        }, 0);
        // Get favorite services (most booked)
        const serviceBookings = appointments.reduce((acc, apt) => {
            const serviceId = apt.service?._id?.toString();
            const serviceName = apt.service?.name;
            if (serviceId && serviceName) {
                if (!acc[serviceId]) {
                    acc[serviceId] = {
                        id: serviceId,
                        name: serviceName,
                        count: 0,
                        price: apt.service?.price || 0,
                        category: apt.service?.category || 'General'
                    };
                }
                acc[serviceId].count++;
            }
            return acc;
        }, {});
        const favoriteServices = Object.values(serviceBookings)
            .sort((a, b) => b.count - a.count)
            .slice(0, 3);
        // Remove password from user object
        const userResponse = user.toObject();
        const { password, ...userWithoutPassword } = userResponse;
        // Send comprehensive dashboard data
        (0, response_1.sendSuccess)(res, 'User dashboard data retrieved successfully', {
            user: userWithoutPassword,
            appointments: {
                all: appointments.map(appointment => ({
                    id: appointment._id,
                    serviceId: appointment.service?._id,
                    serviceName: appointment.service?.name,
                    servicePrice: appointment.service?.price,
                    serviceDuration: appointment.service?.duration,
                    serviceCategory: appointment.service?.category,
                    date: appointment.date,
                    time: appointment.time,
                    status: appointment.status,
                    customerInfo: appointment.customerInfo,
                    notes: appointment.message,
                    createdAt: appointment.createdAt,
                    updatedAt: appointment.updatedAt
                })),
                recent: recentAppointments,
                upcoming: upcomingAppointments
            },
            statistics: {
                appointments: appointmentStats,
                totalSpent,
                favoriteServices,
                memberSince: user.createdAt,
                lastActivity: user.updatedAt
            }
        });
    }
    catch (error) {
        console.error('Get user dashboard data error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/appointments/my - Get user's appointments
router.get('/my', auth_1.authenticate, async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const appointments = await models_1.Appointment.find({ user: req.user._id })
            .populate('service', 'name price duration category')
            .sort({ date: -1, time: -1 });
        const formattedAppointments = appointments.map(appointment => ({
            id: appointment._id,
            serviceId: appointment.service._id,
            serviceName: appointment.service.name,
            servicePrice: appointment.service.price,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            totalPrice: appointment.service.price, // Use service price as total
            notes: appointment.message,
            createdAt: appointment.createdAt
        }));
        (0, response_1.sendSuccess)(res, 'User appointments retrieved successfully', formattedAppointments);
    }
    catch (error) {
        console.error('Get user appointments error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// PUT /api/v2/appointments/:id - Update appointment
router.put('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const { id } = req.params;
        const { date, time, customerInfo, notes } = req.body;
        const appointment = await models_1.Appointment.findOne({
            _id: id,
            user: req.user._id
        });
        if (!appointment) {
            (0, response_1.sendError)(res, 'Appointment not found', undefined, 404);
            return;
        }
        // Only allow updates to pending appointments
        if (appointment.status !== 'pending') {
            (0, response_1.sendError)(res, 'Cannot update confirmed or completed appointments', undefined, 400);
            return;
        }
        // Update fields
        if (date)
            appointment.date = new Date(date);
        if (time)
            appointment.time = time;
        if (customerInfo) {
            appointment.customerInfo = {
                name: customerInfo.firstName && customerInfo.lastName
                    ? `${customerInfo.firstName} ${customerInfo.lastName}`
                    : appointment.customerInfo.name,
                email: customerInfo.email || appointment.customerInfo.email,
                phone: customerInfo.phone || appointment.customerInfo.phone
            };
        }
        if (notes !== undefined)
            appointment.message = notes;
        await appointment.save();
        await appointment.populate('service', 'name price duration category');
        const responseData = {
            id: appointment._id,
            serviceId: appointment.service._id,
            serviceName: appointment.service.name,
            servicePrice: appointment.service.price,
            date: appointment.date,
            time: appointment.time,
            status: appointment.status,
            customerInfo: appointment.customerInfo,
            totalPrice: appointment.service.price,
            notes: appointment.message,
            updatedAt: appointment.updatedAt
        };
        (0, response_1.sendSuccess)(res, 'Appointment updated successfully', responseData);
    }
    catch (error) {
        console.error('Update appointment error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// DELETE /api/v2/appointments/:id - Cancel appointment
router.delete('/:id', auth_1.authenticate, (0, validation_1.validate)((0, validation_2.mongoIdValidation)()), async (req, res) => {
    try {
        if (!req.user) {
            (0, response_1.sendError)(res, 'Authentication required', undefined, 401);
            return;
        }
        const { id } = req.params;
        const appointment = await models_1.Appointment.findOne({
            _id: id,
            user: req.user._id
        });
        if (!appointment) {
            (0, response_1.sendError)(res, 'Appointment not found', undefined, 404);
            return;
        }
        appointment.status = 'cancelled';
        await appointment.save();
        (0, response_1.sendSuccess)(res, 'Appointment cancelled successfully', { id: appointment._id });
    }
    catch (error) {
        console.error('Cancel appointment error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// POST /api/v2/appointments/:id/payment-proof - Add payment proof to existing appointment
router.post('/:id/payment-proof', controllers_1.UploadController.cloudinaryUploadSingle('proofImage'), async (req, res) => {
    try {
        const { id } = req.params;
        const { amount, paymentMethod, notes, email } = req.body;
        if (!req.file) {
            (0, response_1.sendError)(res, 'Payment proof image is required', undefined, 400);
            return;
        }
        // Find the appointment
        const appointment = await models_1.Appointment.findById(id);
        if (!appointment) {
            (0, response_1.sendError)(res, 'Appointment not found', undefined, 404);
            return;
        }
        // Verify email matches appointment customer
        if (appointment.customerInfo.email.toLowerCase() !== email.toLowerCase()) {
            (0, response_1.sendError)(res, 'Email does not match appointment customer', undefined, 403);
            return;
        }
        // Add payment proof to appointment
        const paymentProof = {
            id: new Date().getTime().toString(),
            amount: parseFloat(amount) || appointment.totalPrice,
            paymentMethod: paymentMethod || 'unknown',
            proofImage: req.file.path, // Cloudinary URL
            status: 'pending',
            notes: notes || '',
            createdAt: new Date()
        };
        appointment.paymentProofs.push(paymentProof);
        await appointment.save();
        // Populate service data for response
        await appointment.populate('service', 'name price duration category');
        (0, response_1.sendSuccess)(res, 'Payment proof added successfully', {
            appointmentId: appointment._id,
            paymentProof: paymentProof,
            appointment: {
                id: appointment._id,
                serviceName: appointment.service.name,
                date: appointment.date,
                time: appointment.time,
                status: appointment.status,
                customerInfo: appointment.customerInfo,
                totalPrice: appointment.totalPrice,
                paymentProofs: appointment.paymentProofs
            }
        });
    }
    catch (error) {
        console.error('Add payment proof error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
// GET /api/v2/appointments/user/:userId - Get appointments for a specific user
router.get('/user/:userId', async (req, res) => {
    try {
        const { userId } = req.params;
        const { status, page = 1, limit = 20 } = req.query;
        const pageNum = parseInt(page);
        const limitNum = parseInt(limit);
        const skip = (pageNum - 1) * limitNum;
        // Build filter query
        const filter = { user: userId };
        if (status && status !== 'all') {
            filter.status = status;
        }
        const appointments = await models_1.Appointment.find(filter)
            .populate('service', 'name category description price duration')
            .populate('user', 'firstName lastName email')
            .sort({ date: -1, time: -1 })
            .skip(skip)
            .limit(limitNum);
        const total = await models_1.Appointment.countDocuments(filter);
        (0, response_1.sendSuccess)(res, 'User appointments retrieved successfully', {
            appointments,
            pagination: {
                page: pageNum,
                limit: limitNum,
                total,
                pages: Math.ceil(total / limitNum)
            }
        });
    }
    catch (error) {
        console.error('Get user appointments error:', error);
        (0, response_1.sendError)(res, error.message);
    }
});
exports.default = router;
