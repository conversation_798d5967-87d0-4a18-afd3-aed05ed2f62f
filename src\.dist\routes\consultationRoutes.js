"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const consultationController_1 = require("../controllers/consultationController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const express_validator_1 = require("express-validator");
const router = (0, express_1.Router)();
// Validation for consultation booking
const consultationValidation = [
    // Either name OR (firstName AND lastName) is required
    (0, express_validator_1.body)('name')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('firstName')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('First name must be between 1 and 50 characters'),
    (0, express_validator_1.body)('lastName')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Last name must be between 1 and 50 characters'),
    (0, express_validator_1.body)('email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Please enter a valid email address'),
    (0, express_validator_1.body)('phone')
        .optional()
        .matches(/^[\+]?[1-9][\d]{0,15}$/)
        .withMessage('Please enter a valid phone number'),
    (0, express_validator_1.body)('service')
        .isMongoId()
        .withMessage('Please select a valid service'),
    (0, express_validator_1.body)('date')
        .isISO8601()
        .toDate()
        .custom((value) => {
        if (value < new Date()) {
            throw new Error('Consultation date cannot be in the past');
        }
        return true;
    })
        .withMessage('Please enter a valid future date'),
    (0, express_validator_1.body)('time')
        .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
        .withMessage('Please enter a valid time in HH:MM format'),
    (0, express_validator_1.body)('message')
        .optional()
        .trim()
        .isLength({ max: 500 })
        .withMessage('Message cannot be more than 500 characters'),
    // Custom validation to ensure either name OR (firstName AND lastName) is provided
    (0, express_validator_1.body)().custom((value) => {
        if (!value.name && (!value.firstName || !value.lastName)) {
            throw new Error('Either name or both first name and last name are required');
        }
        return true;
    })
];
// GET /api/consultation/availability
// Public endpoint to get available consultation slots
router.get('/availability', consultationController_1.ConsultationController.getAvailability);
// POST /api/consultation/book
// Public endpoint for guest consultation booking
// This is the main endpoint for the consultation form
router.post('/book', (0, validation_1.validate)(consultationValidation), consultationController_1.ConsultationController.bookGuestConsultation);
// POST /api/consultation/book-authenticated
// Authenticated endpoint for logged-in users
// Optional: for users who are already logged in
router.post('/book-authenticated', auth_1.authenticate, (0, validation_1.validate)(consultationValidation), consultationController_1.ConsultationController.bookConsultation);
exports.default = router;
