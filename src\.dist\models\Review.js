"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.Review = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const reviewSchema = new mongoose_1.Schema({
    user: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User',
        required: false // Allow anonymous reviews for appointments
    },
    appointment: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Appointment',
        required: false // For appointment-based reviews
    },
    product: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Product',
        required: false
    },
    service: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'Service',
        required: false
    },
    customerName: {
        type: String,
        trim: true,
        maxlength: [100, 'Customer name cannot be more than 100 characters']
    },
    customerEmail: {
        type: String,
        trim: true,
        lowercase: true,
        maxlength: [255, 'Customer email cannot be more than 255 characters']
    },
    rating: {
        type: Number,
        required: [true, 'Rating is required'],
        min: [1, 'Rating must be at least 1'],
        max: [5, 'Rating cannot be more than 5']
    },
    title: {
        type: String,
        required: false, // Make title optional for appointment reviews
        trim: true,
        maxlength: [100, 'Title cannot be more than 100 characters']
    },
    comment: {
        type: String,
        required: false, // Make comment optional
        trim: true,
        maxlength: [1000, 'Comment cannot be more than 1000 characters']
    },
    status: {
        type: String,
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending'
    },
    isVerifiedPurchase: {
        type: Boolean,
        default: false
    },
    helpfulVotes: {
        type: Number,
        default: 0
    },
    adminResponse: {
        type: String,
        trim: true,
        maxlength: [500, 'Admin response cannot be more than 500 characters']
    },
    adminResponseDate: {
        type: Date
    }
}, {
    timestamps: true
});
// Indexes for better query performance
reviewSchema.index({ product: 1, status: 1 });
reviewSchema.index({ service: 1, status: 1 });
reviewSchema.index({ appointment: 1 });
reviewSchema.index({ user: 1 });
reviewSchema.index({ status: 1 });
reviewSchema.index({ createdAt: -1 });
// Remove unique constraints to allow multiple reviews
// Users can create multiple reviews for products, services, and appointments
// This allows for multiple reviews over time as experiences may change
// Keep appointment index for performance but remove uniqueness
reviewSchema.index({ appointment: 1 }, { sparse: true });
// Virtual for populated user data
reviewSchema.virtual('userData', {
    ref: 'User',
    localField: 'user',
    foreignField: '_id',
    justOne: true
});
// Virtual for populated product data
reviewSchema.virtual('productData', {
    ref: 'Product',
    localField: 'product',
    foreignField: '_id',
    justOne: true
});
// Virtual for populated service data
reviewSchema.virtual('serviceData', {
    ref: 'Service',
    localField: 'service',
    foreignField: '_id',
    justOne: true
});
// Ensure virtuals are included in JSON output
reviewSchema.set('toJSON', { virtuals: true });
reviewSchema.set('toObject', { virtuals: true });
// Custom validation to ensure either product, service, or appointment is provided
reviewSchema.pre('validate', function (next) {
    const hasProduct = !!this.product;
    const hasService = !!this.service;
    const hasAppointment = !!this.appointment;
    const count = [hasProduct, hasService, hasAppointment].filter(Boolean).length;
    if (count === 0) {
        next(new Error('Review must be associated with either a product, service, or appointment'));
    }
    else if (count > 1) {
        next(new Error('Review can only be associated with one of: product, service, or appointment'));
    }
    else {
        next();
    }
});
exports.Review = mongoose_1.default.model('Review', reviewSchema);
