import { useState, useEffect } from 'react';
import { appointmentAPI } from '../../utils/appointmentAPI';
import {
  type User,
  dashboardAPI,
  clearAuthData,
  type DashboardData,
  type DashboardAppointment
} from '../../utils/api';
import { useToast } from '../../contexts/ToastContext';
import { API_CONFIG } from '../../utils/config';
import '../../styles/admin.css';

// Extended appointment interface with payment proofs
interface ExtendedDashboardAppointment extends DashboardAppointment {
  paymentProofs?: Array<{
    id: string;
    amount: number;
    paymentMethod: string;
    proofImage: string;
    status: 'pending' | 'verified' | 'rejected';
    notes?: string;
    createdAt: string;
  }>;
}

// Review interface
interface Review {
  id: string;
  appointmentId?: string;
  serviceId?: string;
  serviceName?: string;
  serviceCategory?: string;
  appointmentDate?: string;
  appointmentTime?: string;
  rating: number;
  title?: string;
  comment?: string;
  status: 'pending' | 'approved' | 'rejected';
  isVerifiedPurchase: boolean;
  adminResponse?: string;
  adminResponseDate?: string;
  createdAt: string;
  updatedAt: string;
}

interface UserDashboardProps {
  currentUser: User | null;
  onLogout: () => void;
  onBookNew: () => void;
}

export default function UserDashboard({ currentUser, onLogout, onBookNew }: UserDashboardProps) {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [activeTab, setActiveTab] = useState<'upcoming' | 'past' | 'profile' | 'reviews'>('upcoming');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Review-related state
  const [userReviews, setUserReviews] = useState<Review[]>([]);
  const [completedAppointments, setCompletedAppointments] = useState<DashboardAppointment[]>([]);
  const [showReviewForm, setShowReviewForm] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [reviewFormData, setReviewFormData] = useState({
    rating: 5,
    title: '',
    comment: ''
  });

  // Toast notifications
  const { showSuccess, showError } = useToast();

  useEffect(() => {
    if (currentUser) {
      fetchDashboardData();
      if (activeTab === 'reviews') {
        loadReviewsData();
      }
    }
  }, [currentUser, activeTab]);

  // Load reviews data
  const loadReviewsData = async () => {
    if (!currentUser?._id) return;

    try {
      // Load user's reviews using the new endpoint
      const reviewsResponse = await fetch(`${API_CONFIG.BASE_URL}/user/reviews`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (reviewsResponse.ok) {
        const reviewsData = await reviewsResponse.json();
        setUserReviews(reviewsData.data?.reviews || []);
      }

      // Load completed appointments that can be reviewed using the new endpoint
      const appointmentsResponse = await fetch(`${API_CONFIG.BASE_URL}/user/appointments-to-review`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (appointmentsResponse.ok) {
        const appointmentsData = await appointmentsResponse.json();
        setCompletedAppointments(appointmentsData.data || []);
      }
    } catch (error) {
      console.error('Error loading reviews data:', error);
    }
  };

  // Handle review submission
  const handleSubmitReview = async (appointmentId: string) => {
    if (reviewFormData.rating < 1 || reviewFormData.rating > 5) {
      showError('Please select a rating between 1 and 5 stars');
      return;
    }

    setSubmitting(true);
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/reviews`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify({
          appointmentId,
          rating: reviewFormData.rating,
          title: reviewFormData.title.trim() || undefined,
          comment: reviewFormData.comment.trim() || undefined
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to submit review');
      }

      showSuccess('Thank you for your review! It will be reviewed by our team before being published.');
      setShowReviewForm(null);
      setReviewFormData({ rating: 5, title: '', comment: '' });

      // Reload reviews data
      await loadReviewsData();
    } catch (error: any) {
      console.error('Error submitting review:', error);
      showError(error.message || 'Failed to submit review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  // Helper functions for reviews
  const renderStars = (rating: number) => {
    return (
      <div className="rating-stars">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`star ${star <= rating ? 'filled' : 'empty'}`}
          >
            ★
          </span>
        ))}
      </div>
    );
  };

  const renderInteractiveStars = (rating: number, onRatingChange: (rating: number) => void) => {
    return (
      <div className="interactive-stars">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            className={`interactive-star ${star <= rating ? 'filled' : ''}`}
            onClick={() => onRatingChange(star)}
          >
            ★
          </button>
        ))}
        <span className="rating-label">
          {rating} out of 5 stars
        </span>
      </div>
    );
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'approved':
        return 'status-badge approved';
      case 'rejected':
        return 'status-badge rejected';
      case 'pending':
        return 'status-badge pending';
      default:
        return 'status-badge';
    }
  };

  const getAppointmentsToReview = () => {
    const reviewedAppointmentIds = userReviews.map(review => review.appointmentId).filter(Boolean);
    return completedAppointments.filter(apt => !reviewedAppointmentIds.includes(apt.id));
  };

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Try to use the new dashboard API first
      try {
        const response = await dashboardAPI.getUserDashboard();
        console.log('Dashboard API response:', response);

        if (response && response.success && response.data) {
          setDashboardData(response.data);

          // If dashboard includes review data, populate it
          if (response.data.userReviews) {
            setUserReviews(response.data.userReviews);
          }
          if (response.data.completedAppointmentsToReview) {
            setCompletedAppointments(response.data.completedAppointmentsToReview);
          }
        } else {
          throw new Error('Invalid dashboard response format');
        }
      } catch (dashboardError) {
        console.warn('Dashboard API failed, trying appointment API fallback:', dashboardError);

        try {
          const response = await appointmentAPI.getUserAppointments();
          console.log('Appointment API fallback response:', response);

          // Convert old format to new dashboard format
          let appointmentsData: any[] = [];
          if (Array.isArray(response)) {
            appointmentsData = response;
          }

          // Create a basic dashboard data structure from appointments
          const fallbackDashboardData: DashboardData = {
            user: currentUser || { id: '', firstName: 'User', lastName: '', email: '', phone: '', role: 'user', createdAt: '' },
            appointments: {
              all: appointmentsData,
              recent: appointmentsData.slice(0, 5),
              upcoming: appointmentsData.filter((apt: any) =>
                new Date(apt.date) >= new Date() && apt.status !== 'cancelled'
              )
            },
            statistics: {
              appointments: {
                total: appointmentsData.length,
                pending: appointmentsData.filter((apt: any) => apt.status === 'pending').length,
                confirmed: appointmentsData.filter((apt: any) => apt.status === 'confirmed').length,
                completed: appointmentsData.filter((apt: any) => apt.status === 'completed').length,
                cancelled: appointmentsData.filter((apt: any) => apt.status === 'cancelled').length,
              },
              totalSpent: appointmentsData.reduce((sum: number, apt: any) => sum + (apt.totalPrice || apt.servicePrice || 0), 0),
              favoriteServices: [],
              memberSince: currentUser?.createdAt || '',
              lastActivity: currentUser?.updatedAt || ''
            }
          };

          setDashboardData(fallbackDashboardData);
        } catch (appointmentError) {
          console.error('Both APIs failed:', { dashboardError, appointmentError });
          setError('Failed to load dashboard data. Please try again.');
        }
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError('An unexpected error occurred while loading your dashboard.');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelAppointment = async (appointmentId: string) => {
    if (window.confirm('Are you sure you want to cancel this appointment?')) {
      try {
        await appointmentAPI.cancelAppointment(appointmentId);
        await fetchDashboardData(); // Refresh the dashboard data
        alert('Appointment cancelled successfully');
      } catch (error) {
        console.error('Error cancelling appointment:', error);
        alert('Failed to cancel appointment. Please try again.');
      }
    }
  };

  const handleLogout = () => {
    clearAuthData();
    onLogout();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // formatTime function removed as it was unused

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return '#28a745';
      case 'pending': return '#ffc107';
      case 'completed': return '#6c757d';
      case 'cancelled': return '#dc3545';
      default: return '#6c757d';
    }
  };

  // Extract data from dashboard or use empty defaults
  const appointments = dashboardData?.appointments || { all: [], recent: [], upcoming: [] };
  const statistics = dashboardData?.statistics || {
    appointments: { total: 0, pending: 0, confirmed: 0, completed: 0, cancelled: 0 },
    totalSpent: 0,
    favoriteServices: [],
    memberSince: '',
    lastActivity: ''
  };
  const user = dashboardData?.user || currentUser;

  const upcomingAppointments = appointments.upcoming || [];
  const pastAppointments = appointments.all.filter(apt =>
    new Date(apt.date) < new Date() || apt.status === 'completed' || apt.status === 'cancelled'
  ) || [];

  if (!currentUser) {
    return <div>Please log in to view your dashboard.</div>;
  }

  if (loading) {
    return (
      <div className="dashboard-container">
        <div className="loading-container">
          <p>Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="dashboard-container">
        <div className="error-container">
          <h3>Error Loading Dashboard</h3>
          <p>{error}</p>
          <button onClick={fetchDashboardData} className="action-button primary">
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container">
      {/* Header */}
      <header className="dashboard-header">
        <div className="dashboard-header-content">
          <div className="dashboard-logo">
            <h1>dammyspicybeauty</h1>
          </div>
          <div className="dashboard-user-menu">
            <span className="welcome-text">Welcome, {currentUser.firstName || currentUser.name?.split(' ')[0] || 'User'}!</span>
            <button className="logout-button" onClick={handleLogout}>
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="dashboard-main">
        <div className="dashboard-content">
          {/* Quick Actions */}
          <div className="quick-actions">
            <button className="action-button primary" onClick={onBookNew}>
              📅 Book New Appointment
            </button>
            <div className="stats-grid">
              <div className="stat-card">
                <h3>{statistics.appointments.total}</h3>
                <p>Total Appointments</p>
              </div>
              <div className="stat-card">
                <h3>{upcomingAppointments.length}</h3>
                <p>Upcoming</p>
              </div>
              <div className="stat-card">
                <h3>{statistics.appointments.pending}</h3>
                <p>Pending</p>
              </div>
              <div className="stat-card">
                <h3>${statistics.totalSpent.toFixed(2)}</h3>
                <p>Total Spent</p>
              </div>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="dashboard-tabs">
            <button 
              className={`tab-button ${activeTab === 'upcoming' ? 'active' : ''}`}
              onClick={() => setActiveTab('upcoming')}
            >
              Upcoming Appointments ({upcomingAppointments.length})
            </button>
            <button 
              className={`tab-button ${activeTab === 'past' ? 'active' : ''}`}
              onClick={() => setActiveTab('past')}
            >
              Past Appointments ({pastAppointments.length})
            </button>
            <button
              className={`tab-button ${activeTab === 'profile' ? 'active' : ''}`}
              onClick={() => setActiveTab('profile')}
            >
              Profile
            </button>
            <button
              className={`tab-button ${activeTab === 'reviews' ? 'active' : ''}`}
              onClick={() => setActiveTab('reviews')}
            >
              ⭐ My Reviews
            </button>
          </div>

          {/* Tab Content */}
          <div className="tab-content">
            {activeTab === 'upcoming' && (
              <div className="appointments-list">
                {upcomingAppointments.length === 0 ? (
                  <div className="empty-state">
                    <h3>No upcoming appointments</h3>
                    <p>Book your next appointment to get started!</p>
                    <button className="action-button primary" onClick={onBookNew}>
                      Book Appointment
                    </button>
                  </div>
                ) : (
                  upcomingAppointments.map(appointment => (
                    <div key={appointment.id} className="appointment-card">
                      <div className="appointment-header">
                        <h4>{appointment.serviceName}</h4>
                        <span
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(appointment.status) }}
                        >
                          {appointment.status.toUpperCase()}
                        </span>
                      </div>
                      <div className="appointment-details">
                        <p><strong>Date:</strong> {formatDate(appointment.date)}</p>
                        <p><strong>Time:</strong> {appointment.time}</p>
                        <p><strong>Price:</strong> ${appointment.servicePrice.toFixed(2)}</p>
                        {appointment.serviceCategory && (
                          <p><strong>Category:</strong> {appointment.serviceCategory}</p>
                        )}
                        {appointment.serviceDuration && (
                          <p><strong>Duration:</strong> {appointment.serviceDuration} hours</p>
                        )}
                        {appointment.notes && (
                          <div className="appointment-notes">
                            <strong>Notes:</strong> {appointment.notes}
                          </div>
                        )}

                        {/* Payment Proofs Section */}
                        {(appointment as ExtendedDashboardAppointment).paymentProofs && (appointment as ExtendedDashboardAppointment).paymentProofs!.length > 0 && (
                          <div className="payment-proofs-section" style={{
                            marginTop: '1rem',
                            paddingTop: '1rem',
                            borderTop: '1px solid #e5e7eb'
                          }}>
                            <strong style={{ display: 'block', marginBottom: '0.5rem' }}>Payment Proofs:</strong>
                            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
                              {(appointment as ExtendedDashboardAppointment).paymentProofs!.map((proof: any, index: number) => (
                                <button
                                  key={proof.id || index}
                                  onClick={() => {
                                    // Create modal for viewing payment proof
                                    const modal = document.createElement('div');
                                    modal.style.cssText = `
                                      position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                                      background: rgba(0,0,0,0.8); display: flex; align-items: center;
                                      justify-content: center; z-index: 9999; padding: 1rem;
                                    `;
                                    modal.onclick = () => document.body.removeChild(modal);

                                    const content = document.createElement('div');
                                    content.style.cssText = `
                                      background: white; border-radius: 0.75rem; padding: 1.5rem;
                                      max-width: 90vw; max-height: 90vh; overflow: auto;
                                    `;
                                    content.onclick = (e) => e.stopPropagation();

                                    content.innerHTML = `
                                      <h3 style="margin: 0 0 1rem 0; font-size: 1.25rem; font-weight: bold;">Payment Proof</h3>
                                      <div style="margin-bottom: 1rem; font-size: 0.875rem; color: #666;">
                                        <p><strong>Amount:</strong> $${proof.amount}</p>
                                        <p><strong>Method:</strong> ${proof.paymentMethod}</p>
                                        <p><strong>Status:</strong> ${proof.status}</p>
                                        <p><strong>Date:</strong> ${new Date(proof.createdAt).toLocaleDateString()}</p>
                                        ${proof.notes ? `<p><strong>Notes:</strong> ${proof.notes}</p>` : ''}
                                      </div>
                                      <img src="${proof.proofImage}" alt="Payment Proof" style="max-width: 100%; max-height: 60vh; object-fit: contain; border-radius: 0.5rem;" />
                                    `;

                                    modal.appendChild(content);
                                    document.body.appendChild(modal);
                                  }}
                                  style={{
                                    padding: '0.25rem 0.75rem',
                                    backgroundColor: proof.status === 'verified' ? '#dcfce7' :
                                                   proof.status === 'rejected' ? '#fef2f2' : '#fef3c7',
                                    color: proof.status === 'verified' ? '#166534' :
                                           proof.status === 'rejected' ? '#dc2626' : '#92400e',
                                    border: 'none',
                                    borderRadius: '0.375rem',
                                    fontSize: '0.75rem',
                                    cursor: 'pointer',
                                    fontWeight: '500'
                                  }}
                                >
                                  📄 ${proof.paymentMethod} (${proof.status})
                                </button>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                      <div className="appointment-actions">
                        {appointment.status === 'pending' && (
                          <button
                            className="action-button danger"
                            onClick={() => handleCancelAppointment(appointment.id)}
                          >
                            Cancel Appointment
                          </button>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'past' && (
              <div className="appointments-list">
                {pastAppointments.length === 0 ? (
                  <div className="empty-state">
                    <h3>No past appointments</h3>
                    <p>Your appointment history will appear here.</p>
                  </div>
                ) : (
                  pastAppointments.map(appointment => (
                    <div key={appointment.id} className="appointment-card past">
                      <div className="appointment-header">
                        <h4>{appointment.serviceName}</h4>
                        <span
                          className="status-badge"
                          style={{ backgroundColor: getStatusColor(appointment.status) }}
                        >
                          {appointment.status.toUpperCase()}
                        </span>
                      </div>
                      <div className="appointment-details">
                        <p><strong>Date:</strong> {formatDate(appointment.date)}</p>
                        <p><strong>Time:</strong> {appointment.time}</p>
                        <p><strong>Price:</strong> ${appointment.servicePrice.toFixed(2)}</p>
                        {appointment.serviceCategory && (
                          <p><strong>Category:</strong> {appointment.serviceCategory}</p>
                        )}
                        {appointment.serviceDuration && (
                          <p><strong>Duration:</strong> {appointment.serviceDuration} hours</p>
                        )}
                        {appointment.notes && (
                          <div className="appointment-notes">
                            <strong>Notes:</strong> {appointment.notes}
                          </div>
                        )}

                        {/* Payment Proofs Section */}
                        {(appointment as ExtendedDashboardAppointment).paymentProofs && (appointment as ExtendedDashboardAppointment).paymentProofs!.length > 0 && (
                          <div className="payment-proofs-section" style={{
                            marginTop: '1rem',
                            paddingTop: '1rem',
                            borderTop: '1px solid #e5e7eb'
                          }}>
                            <strong style={{ display: 'block', marginBottom: '0.5rem' }}>Payment Proofs:</strong>
                            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '0.5rem' }}>
                              {(appointment as ExtendedDashboardAppointment).paymentProofs!.map((proof: any, index: number) => (
                                <button
                                  key={proof.id || index}
                                  onClick={() => {
                                    // Create modal for viewing payment proof
                                    const modal = document.createElement('div');
                                    modal.style.cssText = `
                                      position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                                      background: rgba(0,0,0,0.8); display: flex; align-items: center;
                                      justify-content: center; z-index: 9999; padding: 1rem;
                                    `;
                                    modal.onclick = () => document.body.removeChild(modal);

                                    const content = document.createElement('div');
                                    content.style.cssText = `
                                      background: white; border-radius: 0.75rem; padding: 1.5rem;
                                      max-width: 90vw; max-height: 90vh; overflow: auto;
                                    `;
                                    content.onclick = (e) => e.stopPropagation();

                                    content.innerHTML = `
                                      <h3 style="margin: 0 0 1rem 0; font-size: 1.25rem; font-weight: bold;">Payment Proof</h3>
                                      <div style="margin-bottom: 1rem; font-size: 0.875rem; color: #666;">
                                        <p><strong>Amount:</strong> $${proof.amount}</p>
                                        <p><strong>Method:</strong> ${proof.paymentMethod}</p>
                                        <p><strong>Status:</strong> ${proof.status}</p>
                                        <p><strong>Date:</strong> ${new Date(proof.createdAt).toLocaleDateString()}</p>
                                        ${proof.notes ? `<p><strong>Notes:</strong> ${proof.notes}</p>` : ''}
                                      </div>
                                      <img src="${proof.proofImage}" alt="Payment Proof" style="max-width: 100%; max-height: 60vh; object-fit: contain; border-radius: 0.5rem;" />
                                    `;

                                    modal.appendChild(content);
                                    document.body.appendChild(modal);
                                  }}
                                  style={{
                                    padding: '0.25rem 0.75rem',
                                    backgroundColor: proof.status === 'verified' ? '#dcfce7' :
                                                   proof.status === 'rejected' ? '#fef2f2' : '#fef3c7',
                                    color: proof.status === 'verified' ? '#166534' :
                                           proof.status === 'rejected' ? '#dc2626' : '#92400e',
                                    border: 'none',
                                    borderRadius: '0.375rem',
                                    fontSize: '0.75rem',
                                    cursor: 'pointer',
                                    fontWeight: '500'
                                  }}
                                >
                                  📄 ${proof.paymentMethod} (${proof.status})
                                </button>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="profile-section">
                <div className="profile-card">
                  <h3>Personal Information</h3>
                  <div className="profile-info">
                    <div className="info-row">
                      <label>Name:</label>
                      <span>{user?.name || `${user?.firstName || ''} ${user?.lastName || ''}`.trim()}</span>
                    </div>
                    <div className="info-row">
                      <label>Email:</label>
                      <span>{user?.email}</span>
                    </div>
                    <div className="info-row">
                      <label>Phone:</label>
                      <span>{user?.phone || 'Not provided'}</span>
                    </div>
                    <div className="info-row">
                      <label>Role:</label>
                      <span>{user?.role}</span>
                    </div>
                    <div className="info-row">
                      <label>Verified:</label>
                      <span>{user?.isVerified ? 'Yes' : 'No'}</span>
                    </div>
                    <div className="info-row">
                      <label>Member Since:</label>
                      <span>{statistics.memberSince ? formatDate(statistics.memberSince) : 'N/A'}</span>
                    </div>
                    <div className="info-row">
                      <label>Last Activity:</label>
                      <span>{statistics.lastActivity ? formatDate(statistics.lastActivity) : 'N/A'}</span>
                    </div>
                  </div>
                  <button className="action-button secondary">
                    Edit Profile
                  </button>
                </div>

                {/* Favorite Services */}
                {statistics.favoriteServices.length > 0 && (
                  <div className="profile-card">
                    <h3>Favorite Services</h3>
                    <div className="favorite-services">
                      {statistics.favoriteServices.map(service => (
                        <div key={service.id} className="favorite-service">
                          <div className="service-info">
                            <h4>{service.name}</h4>
                            <p>Category: {service.category}</p>
                            <p>Price: ${service.price}</p>
                            <p>Booked {service.count} time{service.count !== 1 ? 's' : ''}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Notification Preferences */}
                {user?.notificationPreferences && (
                  <div className="profile-card">
                    <h3>Notification Preferences</h3>
                    <div className="notification-prefs">
                      <div className="pref-row">
                        <label>Email Notifications:</label>
                        <span>{user.notificationPreferences.email ? 'Enabled' : 'Disabled'}</span>
                      </div>
                      <div className="pref-row">
                        <label>SMS Notifications:</label>
                        <span>{user.notificationPreferences.sms ? 'Enabled' : 'Disabled'}</span>
                      </div>
                      <div className="pref-row">
                        <label>Push Notifications:</label>
                        <span>{user.notificationPreferences.push ? 'Enabled' : 'Disabled'}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="reviews-section">
                {/* Appointments to Review */}
                {getAppointmentsToReview().length > 0 && (
                  <div className="dashboard-section">
                    <h3 className="section-title">Leave a Review</h3>
                    <p className="section-subtitle">Rate your recent completed appointments</p>

                    <div className="appointments-grid">
                      {getAppointmentsToReview().map((appointment) => (
                        <div key={appointment.id} className="appointment-card">
                          <div className="appointment-header">
                            <h4 className="appointment-service">{appointment.serviceName}</h4>
                            <span className="appointment-date">
                              {formatDate(appointment.date)}
                            </span>
                          </div>

                          <div className="appointment-details">
                            <p className="service-category">{appointment.serviceCategory}</p>
                            <p className="appointment-time">Time: {appointment.time}</p>
                            <p className="appointment-price">Price: ${appointment.servicePrice}</p>
                          </div>

                          {showReviewForm === appointment.id ? (
                            <div className="review-form-container">
                              <h4 className="review-form-title">Write Your Review</h4>
                              <form onSubmit={(e) => {
                                e.preventDefault();
                                handleSubmitReview(appointment.id);
                              }}>
                                <div className="form-group">
                                  <label className="form-label">Rating *</label>
                                  {renderInteractiveStars(reviewFormData.rating, (rating) =>
                                    setReviewFormData(prev => ({ ...prev, rating }))
                                  )}
                                </div>

                                <div className="form-group">
                                  <label className="form-label">Review Title (Optional)</label>
                                  <input
                                    type="text"
                                    value={reviewFormData.title}
                                    onChange={(e) => setReviewFormData(prev => ({ ...prev, title: e.target.value }))}
                                    className="form-input"
                                    placeholder="Summarize your experience..."
                                    maxLength={100}
                                  />
                                </div>

                                <div className="form-group">
                                  <label className="form-label">Your Review (Optional)</label>
                                  <textarea
                                    value={reviewFormData.comment}
                                    onChange={(e) => setReviewFormData(prev => ({ ...prev, comment: e.target.value }))}
                                    rows={4}
                                    className="form-input form-textarea"
                                    placeholder="Tell others about your experience..."
                                    maxLength={1000}
                                  />
                                </div>

                                <div className="form-actions">
                                  <button
                                    type="submit"
                                    disabled={submitting}
                                    className="btn-submit"
                                  >
                                    {submitting ? 'Submitting...' : 'Submit Review'}
                                  </button>
                                  <button
                                    type="button"
                                    onClick={() => setShowReviewForm(null)}
                                    className="btn-cancel"
                                  >
                                    Cancel
                                  </button>
                                </div>
                              </form>
                            </div>
                          ) : (
                            <div className="appointment-actions">
                              <button
                                onClick={() => setShowReviewForm(appointment.id)}
                                className="write-review-btn"
                              >
                                Write Review
                              </button>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* User's Reviews */}
                {userReviews.length > 0 && (
                  <div className="dashboard-section">
                    <h3 className="section-title">Your Reviews</h3>
                    <p className="section-subtitle">Reviews you've submitted</p>

                    <div className="reviews-grid">
                      {userReviews.map((review) => (
                        <div key={review.id} className="review-card">
                          <div className="review-header">
                            <div className="review-service-info">
                              <h4 className="review-service-name">{review.serviceName}</h4>
                              <span className="review-date">
                                {new Date(review.createdAt).toLocaleDateString()}
                              </span>
                            </div>
                            <div className="review-status">
                              {renderStars(review.rating)}
                              <span className={getStatusBadgeClass(review.status)}>
                                {review.status}
                              </span>
                            </div>
                          </div>

                          {review.title && (
                            <h5 className="review-title">{review.title}</h5>
                          )}

                          {review.comment && (
                            <p className="review-comment">{review.comment}</p>
                          )}

                          {review.adminResponse && (
                            <div className="admin-response">
                              <div className="admin-response-label">Business Response:</div>
                              <div className="admin-response-text">{review.adminResponse}</div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Empty State */}
                {getAppointmentsToReview().length === 0 && userReviews.length === 0 && (
                  <div className="empty-state">
                    <div className="empty-state-icon">⭐</div>
                    <h3 className="empty-state-title">No Reviews Yet</h3>
                    <p className="empty-state-subtitle">
                      Complete an appointment to leave your first review!
                    </p>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
}
