import { Router } from 'express';
import { Request, Response } from 'express';
import { Service } from '../../models';
import { sendSuccess, sendError } from '../../utils/response';

const router = Router();

// GET /api/v2/services - Get all services formatted for frontend v2
router.get('/', async (req: Request, res: Response) => {
  try {
    const services = await Service.find({ isActive: true }).sort({ category: 1, name: 1 });

    // Define custom service order within categories
    const getServiceOrder = (categoryKey: string, serviceName: string): number => {
      if (categoryKey === 'installation') {
        // Installation order: Extra Small → Small → Medium → Instant Lock
        const installationOrder = [
          'EXTRA SMALL MICROLOCS',
          'SMALL SIZE MICROLOCS',
          'MEDIUM MICROLOCS',
          'INSTANT LOCK'
        ];
        const index = installationOrder.indexOf(serviceName);
        return index === -1 ? 999 : index;
      } else if (categoryKey.includes('retightening')) {
        // Retightening order: Extra Small → Small → Medium
        if (serviceName.includes('Extra Small')) return 0;
        if (serviceName.includes('Small') && !serviceName.includes('Extra Small')) return 1;
        if (serviceName.includes('Medium')) return 2;
        return 999;
      }
      return 0; // Default order for other categories
    };

    // Group services by category to match frontend v2 structure
    const groupedServices: { [key: string]: any[] } = {};

    services.forEach(service => {
      const categoryKey = service.category.toLowerCase().replace(/\s+/g, '');

      if (!groupedServices[categoryKey]) {
        groupedServices[categoryKey] = [];
      }

      groupedServices[categoryKey].push({
        id: service._id,
        name: service.name,
        price: service.price.toFixed(2),
        description: service.description,
        duration: service.duration,
        category: service.category,
        image: service.image,
        images: service.images
      });
    });

    // Sort services within each category using custom order
    Object.keys(groupedServices).forEach(categoryKey => {
      groupedServices[categoryKey].sort((a, b) => {
        const orderA = getServiceOrder(categoryKey, a.name);
        const orderB = getServiceOrder(categoryKey, b.name);
        return orderA - orderB;
      });
    });

    sendSuccess(res, 'Services retrieved successfully', groupedServices);
  } catch (error) {
    console.error('Get services error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/services/categories - Get service categories
router.get('/categories', async (req: Request, res: Response) => {
  try {
    const categories = await Service.distinct('category', { isActive: true });
    sendSuccess(res, 'Service categories retrieved successfully', categories);
  } catch (error) {
    console.error('Get service categories error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/services/:id - Get service by ID
router.get('/:id', async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const service = await Service.findById(id);
    
    if (!service) {
      sendError(res, 'Service not found', undefined, 404);
      return;
    }
    
    if (!service.isActive) {
      sendError(res, 'Service is not available', undefined, 404);
      return;
    }
    
    const formattedService = {
      id: service._id,
      name: service.name,
      price: service.price.toFixed(2),
      description: service.description,
      duration: service.duration,
      category: service.category,
      image: service.image,
      images: service.images
    };
    
    sendSuccess(res, 'Service retrieved successfully', formattedService);
  } catch (error) {
    console.error('Get service error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/services/category/:category - Get services by category
router.get('/category/:category', async (req: Request, res: Response) => {
  try {
    const { category } = req.params;
    const services = await Service.find({ 
      category: new RegExp(category, 'i'), 
      isActive: true 
    }).sort({ name: 1 });
    
    const formattedServices = services.map(service => ({
      id: service._id,
      name: service.name,
      price: service.price.toFixed(2),
      description: service.description,
      duration: service.duration,
      category: service.category,
      image: service.image,
      images: service.images
    }));
    
    sendSuccess(res, 'Services retrieved successfully', formattedServices);
  } catch (error) {
    console.error('Get services by category error:', error);
    sendError(res, (error as Error).message);
  }
});

export default router;
