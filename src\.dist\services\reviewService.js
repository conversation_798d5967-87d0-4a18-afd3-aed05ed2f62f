"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewService = void 0;
const Review_1 = require("../models/Review");
const Order_1 = require("../models/Order");
const Appointment_1 = require("../models/Appointment");
class ReviewService {
    /**
     * Create a new review
     */
    static async createReview(reviewData) {
        // Remove duplicate check - allow users to create multiple reviews
        // Users should be able to review the same item multiple times as their experience may change
        // Check if this is a verified purchase
        let isVerifiedPurchase = false;
        if (reviewData.product) {
            // Check if user has purchased this product
            const order = await Order_1.Order.findOne({
                user: reviewData.user,
                'items.product': reviewData.product,
                status: 'delivered'
            });
            isVerifiedPurchase = !!order;
        }
        else if (reviewData.service) {
            // Check if user has booked this service
            const appointment = await Appointment_1.Appointment.findOne({
                user: reviewData.user,
                service: reviewData.service,
                status: 'completed'
            });
            isVerifiedPurchase = !!appointment;
        }
        const review = new Review_1.Review({
            ...reviewData,
            isVerifiedPurchase,
            status: 'pending' // All reviews start as pending for admin approval
        });
        return await review.save();
    }
    /**
     * Get reviews for a product or service
     */
    static async getReviews(params) {
        const { product, service, status = 'approved', page = 1, limit = 10 } = params;
        const query = { status };
        if (product)
            query.product = product;
        if (service)
            query.service = service;
        const skip = (page - 1) * limit;
        const [reviews, total] = await Promise.all([
            Review_1.Review.find(query)
                .populate('user', 'firstName lastName name')
                .populate('product', 'name')
                .populate('service', 'name')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit),
            Review_1.Review.countDocuments(query)
        ]);
        return {
            reviews,
            total,
            page,
            totalPages: Math.ceil(total / limit),
            hasMore: page * limit < total
        };
    }
    /**
     * Get review statistics for a product or service
     */
    static async getReviewStats(productId, serviceId) {
        const query = { status: 'approved' };
        if (productId)
            query.product = productId;
        if (serviceId)
            query.service = serviceId;
        const stats = await Review_1.Review.aggregate([
            { $match: query },
            {
                $group: {
                    _id: null,
                    totalReviews: { $sum: 1 },
                    averageRating: { $avg: '$rating' },
                    ratingDistribution: {
                        $push: '$rating'
                    }
                }
            }
        ]);
        if (stats.length === 0) {
            return {
                totalReviews: 0,
                averageRating: 0,
                ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
            };
        }
        const { totalReviews, averageRating, ratingDistribution } = stats[0];
        // Calculate rating distribution
        const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
        ratingDistribution.forEach((rating) => {
            distribution[rating]++;
        });
        return {
            totalReviews,
            averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
            ratingDistribution: distribution
        };
    }
    /**
     * Update review status (admin only)
     */
    static async updateReviewStatus(reviewId, status) {
        return await Review_1.Review.findByIdAndUpdate(reviewId, { status }, { new: true }).populate('user', 'firstName lastName name')
            .populate('product', 'name')
            .populate('service', 'name');
    }
    /**
     * Delete a review
     */
    static async deleteReview(reviewId, userId) {
        const query = { _id: reviewId };
        if (userId)
            query.user = userId; // Users can only delete their own reviews
        const result = await Review_1.Review.deleteOne(query);
        return result.deletedCount > 0;
    }
    /**
     * Get user's reviews
     */
    static async getUserReviews(userId, page = 1, limit = 10) {
        const skip = (page - 1) * limit;
        const [reviews, total] = await Promise.all([
            Review_1.Review.find({ user: userId })
                .populate('product', 'name image')
                .populate('service', 'name')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit),
            Review_1.Review.countDocuments({ user: userId })
        ]);
        return {
            reviews,
            total,
            page,
            totalPages: Math.ceil(total / limit),
            hasMore: page * limit < total
        };
    }
    /**
     * Get all reviews for admin management
     */
    static async getAllReviews(params) {
        const { status, page = 1, limit = 20, search } = params;
        const query = {};
        if (status && status !== 'all')
            query.status = status;
        // Add search functionality
        if (search) {
            query.$or = [
                { title: { $regex: search, $options: 'i' } },
                { comment: { $regex: search, $options: 'i' } }
            ];
        }
        const skip = (page - 1) * limit;
        const [reviews, total] = await Promise.all([
            Review_1.Review.find(query)
                .populate('user', 'firstName lastName name email')
                .populate('product', 'name')
                .populate('service', 'name')
                .sort({ createdAt: -1 })
                .skip(skip)
                .limit(limit),
            Review_1.Review.countDocuments(query)
        ]);
        return {
            reviews,
            total,
            page,
            totalPages: Math.ceil(total / limit),
            hasMore: page * limit < total
        };
    }
    /**
     * Update a review (user can edit their own review)
     */
    static async updateReview(reviewId, userId, updateData) {
        return await Review_1.Review.findOneAndUpdate({ _id: reviewId, user: userId }, { ...updateData, status: 'pending' }, // Reset to pending when edited
        { new: true }).populate('user', 'firstName lastName name')
            .populate('product', 'name')
            .populate('service', 'name');
    }
}
exports.ReviewService = ReviewService;
