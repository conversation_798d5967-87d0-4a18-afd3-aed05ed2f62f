import { useState, useEffect } from 'react';
import { API_CONFIG } from '../../utils/config';

export default function AboutSection() {
  const [aboutContent, setAboutContent] = useState({
    title: 'About Me',
    text: `Thank you for choosing Dammy<PERSON><PERSON>y Beauty.

My name is <PERSON><PERSON>, and I am a licensed cosmetologist specializing in hair care and beauty treatments for natural hair, including microlocs and more. Based in Indianapolis, IN, my passion is helping women embrace their natural beauty with confidence.

My main objective is to bring out the beauty in each individual, put smiles on faces, and create styles that reflect uniqueness and elegance.

I'm excited to begin this healthy hair journey with you!`
  });

  useEffect(() => {
    // Load about content from branding API
    const loadAboutContent = async () => {
      try {
        const response = await fetch(`${API_CONFIG.BASE_URL}/branding`);
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data?.home) {
            setAboutContent({
              title: data.data.home.aboutTitle || 'About Me',
              text: data.data.home.aboutText || aboutContent.text
            });
          }
        }
      } catch (error) {
        console.error('Error loading about content:', error);
        // Keep default content if API fails
      }
    };

    loadAboutContent();
  }, []);

  return (
    <div className="info-card about-me-card">
      <h3 className="info-title">{aboutContent.title}</h3>
      <div className="info-content">
        {aboutContent.text.includes('<p>') ? (
          <div dangerouslySetInnerHTML={{ __html: aboutContent.text }} />
        ) : (
          aboutContent.text.split('\n\n').map((paragraph, index) => (
            <p key={index}>{paragraph}</p>
          ))
        )}
      </div>
    </div>
  )
}
