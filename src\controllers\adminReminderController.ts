import { Request, Response } from 'express';
import { appointmentReminderService } from '../services/appointmentReminderService';
import { sendSuccess, sendError } from '../utils/response';
import { AuthenticatedRequest } from '../types';

export class AdminReminderController {
  // Get reminder statistics for admin dashboard
  static async getReminderStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const stats = appointmentReminderService.getReminderStats();
      
      sendSuccess(res, 'Reminder statistics retrieved successfully', stats);
    } catch (error) {
      console.error('Get reminder stats error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Get reminder logs for admin dashboard
  static async getReminderLogs(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { limit = 50 } = req.query;
      const logs = appointmentReminderService.getReminderLogs(Number(limit));
      
      sendSuccess(res, 'Reminder logs retrieved successfully', {
        logs,
        total: logs.length
      });
    } catch (error) {
      console.error('Get reminder logs error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Send test reminder for specific appointment
  static async sendTestReminder(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { appointmentId } = req.params;
      const { reminderType } = req.body;

      if (!appointmentId) {
        sendError(res, 'Appointment ID is required', undefined, 400);
        return;
      }

      if (!reminderType || !['48h', '24h'].includes(reminderType)) {
        sendError(res, 'Valid reminder type (48h or 24h) is required', undefined, 400);
        return;
      }

      const success = await appointmentReminderService.sendTestReminder(appointmentId, reminderType);

      if (success) {
        sendSuccess(res, `Test ${reminderType} reminder sent successfully`);
      } else {
        sendError(res, 'Failed to send test reminder', undefined, 500);
      }
    } catch (error) {
      console.error('Send test reminder error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Trigger manual reminder check (for testing)
  static async triggerReminderCheck(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { type } = req.body;

      if (!type || !['48h', '24h', 'both'].includes(type)) {
        sendError(res, 'Valid reminder type (48h, 24h, or both) is required', undefined, 400);
        return;
      }

      let results: any = {};

      if (type === '48h' || type === 'both') {
        await appointmentReminderService.send48HourReminders();
        results.fortyEightHour = 'completed';
      }

      if (type === '24h' || type === 'both') {
        await appointmentReminderService.send24HourReminders();
        results.twentyFourHour = 'completed';
      }

      sendSuccess(res, 'Manual reminder check completed', results);
    } catch (error) {
      console.error('Trigger reminder check error:', error);
      sendError(res, (error as Error).message);
    }
  }

  // Get reminder dashboard data
  static async getReminderDashboard(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const stats = appointmentReminderService.getReminderStats();
      const recentLogs = appointmentReminderService.getReminderLogs(20);

      const dashboardData = {
        stats,
        recentActivity: recentLogs,
        summary: {
          totalReminders: stats.total48h + stats.total24h,
          successRate: stats.total48h + stats.total24h > 0 
            ? Math.round(((stats.successful48h + stats.successful24h) / (stats.total48h + stats.total24h)) * 100)
            : 0,
          last24Hours: stats.last24Hours,
          failedReminders: stats.failed48h + stats.failed24h
        }
      };

      sendSuccess(res, 'Reminder dashboard data retrieved successfully', dashboardData);
    } catch (error) {
      console.error('Get reminder dashboard error:', error);
      sendError(res, (error as Error).message);
    }
  }
}
