"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.appointmentReminderService = void 0;
const node_cron_1 = __importDefault(require("node-cron"));
const models_1 = require("../models");
const emailService_1 = require("./emailService");
const date_fns_1 = require("date-fns");
class AppointmentReminderService {
    constructor() {
        this.reminderLogs = [];
        this.isInitialized = false;
    }
    // Initialize the reminder service with cron jobs
    init() {
        if (this.isInitialized) {
            console.log('Appointment reminder service already initialized');
            return;
        }
        console.log('Initializing appointment reminder service...');
        // Run 48-hour reminders every hour
        node_cron_1.default.schedule('0 * * * *', () => {
            this.send48HourReminders();
        });
        // Run 24-hour reminders every hour
        node_cron_1.default.schedule('0 * * * *', () => {
            this.send24HourReminders();
        });
        // Clean up old logs daily at midnight
        node_cron_1.default.schedule('0 0 * * *', () => {
            this.cleanupOldLogs();
        });
        this.isInitialized = true;
        console.log('Appointment reminder service initialized successfully');
    }
    // Send 48-hour reminders
    async send48HourReminders() {
        try {
            console.log('Checking for 48-hour reminders...');
            const targetDate = (0, date_fns_1.addHours)(new Date(), 48);
            const startWindow = (0, date_fns_1.addHours)(targetDate, -1); // 1 hour before target
            const endWindow = (0, date_fns_1.addHours)(targetDate, 1); // 1 hour after target
            const appointments = await models_1.Appointment.find({
                date: {
                    $gte: startWindow,
                    $lte: endWindow
                },
                status: { $in: ['pending', 'confirmed'] }
            }).populate('user', 'email firstName lastName name');
            console.log(`Found ${appointments.length} appointments for 48-hour reminders`);
            for (const appointment of appointments) {
                // Check if 48-hour reminder already sent
                const alreadySent = this.reminderLogs.some(log => log.appointmentId === appointment._id.toString() &&
                    log.reminderType === '48h' &&
                    log.status === 'sent');
                if (alreadySent) {
                    continue;
                }
                try {
                    if (appointment.user && appointment.user.email) {
                        await emailService_1.emailService.send48HourReminder(appointment.user, appointment);
                        this.logReminder({
                            appointmentId: appointment._id.toString(),
                            reminderType: '48h',
                            sentAt: new Date(),
                            status: 'sent'
                        });
                        console.log(`48-hour reminder sent to ${appointment.user.email}`);
                    }
                }
                catch (error) {
                    console.error(`Failed to send 48-hour reminder for appointment ${appointment._id}:`, error);
                    this.logReminder({
                        appointmentId: appointment._id.toString(),
                        reminderType: '48h',
                        sentAt: new Date(),
                        status: 'failed',
                        error: error.message
                    });
                }
            }
            console.log('48-hour reminders completed');
        }
        catch (error) {
            console.error('Error sending 48-hour reminders:', error);
        }
    }
    // Send 24-hour reminders
    async send24HourReminders() {
        try {
            console.log('Checking for 24-hour reminders...');
            const targetDate = (0, date_fns_1.addHours)(new Date(), 24);
            const startWindow = (0, date_fns_1.addHours)(targetDate, -1); // 1 hour before target
            const endWindow = (0, date_fns_1.addHours)(targetDate, 1); // 1 hour after target
            const appointments = await models_1.Appointment.find({
                date: {
                    $gte: startWindow,
                    $lte: endWindow
                },
                status: { $in: ['pending', 'confirmed'] }
            }).populate('user', 'email firstName lastName name');
            console.log(`Found ${appointments.length} appointments for 24-hour reminders`);
            for (const appointment of appointments) {
                // Check if 24-hour reminder already sent
                const alreadySent = this.reminderLogs.some(log => log.appointmentId === appointment._id.toString() &&
                    log.reminderType === '24h' &&
                    log.status === 'sent');
                if (alreadySent) {
                    continue;
                }
                try {
                    if (appointment.user && appointment.user.email) {
                        await emailService_1.emailService.sendAppointmentReminder(appointment.user, appointment);
                        this.logReminder({
                            appointmentId: appointment._id.toString(),
                            reminderType: '24h',
                            sentAt: new Date(),
                            status: 'sent'
                        });
                        console.log(`24-hour reminder sent to ${appointment.user.email}`);
                    }
                }
                catch (error) {
                    console.error(`Failed to send 24-hour reminder for appointment ${appointment._id}:`, error);
                    this.logReminder({
                        appointmentId: appointment._id.toString(),
                        reminderType: '24h',
                        sentAt: new Date(),
                        status: 'failed',
                        error: error.message
                    });
                }
            }
            console.log('24-hour reminders completed');
        }
        catch (error) {
            console.error('Error sending 24-hour reminders:', error);
        }
    }
    // Log reminder attempts
    logReminder(log) {
        this.reminderLogs.push(log);
    }
    // Get reminder logs for admin dashboard
    getReminderLogs(limit = 100) {
        return this.reminderLogs
            .sort((a, b) => b.sentAt.getTime() - a.sentAt.getTime())
            .slice(0, limit);
    }
    // Get reminder statistics
    getReminderStats() {
        const last24Hours = (0, date_fns_1.addHours)(new Date(), -24);
        const logs48h = this.reminderLogs.filter(log => log.reminderType === '48h');
        const logs24h = this.reminderLogs.filter(log => log.reminderType === '24h');
        const recentLogs = this.reminderLogs.filter(log => (0, date_fns_1.isAfter)(log.sentAt, last24Hours));
        return {
            total48h: logs48h.length,
            successful48h: logs48h.filter(log => log.status === 'sent').length,
            failed48h: logs48h.filter(log => log.status === 'failed').length,
            total24h: logs24h.length,
            successful24h: logs24h.filter(log => log.status === 'sent').length,
            failed24h: logs24h.filter(log => log.status === 'failed').length,
            last24Hours: recentLogs.length
        };
    }
    // Clean up old logs (keep only last 30 days)
    cleanupOldLogs() {
        const cutoffDate = (0, date_fns_1.addDays)(new Date(), -30);
        const initialCount = this.reminderLogs.length;
        this.reminderLogs = this.reminderLogs.filter(log => (0, date_fns_1.isAfter)(log.sentAt, cutoffDate));
        const removedCount = initialCount - this.reminderLogs.length;
        if (removedCount > 0) {
            console.log(`Cleaned up ${removedCount} old reminder logs`);
        }
    }
    // Manual trigger for testing
    async sendTestReminder(appointmentId, reminderType) {
        try {
            const appointment = await models_1.Appointment.findById(appointmentId)
                .populate('user', 'email firstName lastName name');
            if (!appointment || !appointment.user) {
                throw new Error('Appointment or user not found');
            }
            if (reminderType === '48h') {
                await emailService_1.emailService.send48HourReminder(appointment.user, appointment);
            }
            else {
                await emailService_1.emailService.sendAppointmentReminder(appointment.user, appointment);
            }
            this.logReminder({
                appointmentId: appointmentId,
                reminderType,
                sentAt: new Date(),
                status: 'sent'
            });
            return true;
        }
        catch (error) {
            console.error(`Failed to send test reminder:`, error);
            this.logReminder({
                appointmentId: appointmentId,
                reminderType,
                sentAt: new Date(),
                status: 'failed',
                error: error.message
            });
            return false;
        }
    }
}
exports.appointmentReminderService = new AppointmentReminderService();
