import { Router } from 'express';
import { Request, Response } from 'express';
import { format } from 'date-fns';
import { Appointment, Service, User } from '../../models';
import { authenticate, authorize } from '../../middleware/auth';
import { validate } from '../../middleware/validation';
import { mongoIdValidation, paginationValidation } from '../../utils/validation';
import { sendSuccess, sendError, sendCreated } from '../../utils/response';
import { AuthenticatedRequest } from '../../types';
import { UploadController } from '../../controllers';
import { availabilityService } from '../../services/availabilityService';

const router = Router();

// GET /api/v2/appointments - Get all appointments (admin only)
router.get('/', authenticate, authorize('admin'), validate(paginationValidation), async (req: AuthenticatedRequest, res: Response) => {
  try {
    const {
      status,
      date,
      page = 1,
      limit = 20,
      search
    } = req.query as any;

    const filter: any = {};

    if (status) {
      filter.status = status;
    }

    if (date) {
      const searchDate = new Date(date);
      filter.date = {
        $gte: searchDate,
        $lt: new Date(searchDate.getTime() + 24 * 60 * 60 * 1000)
      };
    }

    if (search) {
      filter.$or = [
        { 'customerInfo.name': { $regex: search, $options: 'i' } },
        { 'customerInfo.email': { $regex: search, $options: 'i' } },
        { 'customerInfo.phone': { $regex: search, $options: 'i' } }
      ];
    }

    const pageNum = Number(page);
    const limitNum = Number(limit);
    const skip = (pageNum - 1) * limitNum;

    const [appointments, total] = await Promise.all([
      Appointment.find(filter)
        .populate('user', 'name email phone')
        .populate('service', 'name duration price category')
        .sort({ date: -1, time: -1 })
        .skip(skip)
        .limit(limitNum),
      Appointment.countDocuments(filter)
    ]);

    const totalPages = Math.ceil(total / limitNum);

    // Format appointments for v2 response
    const formattedAppointments = appointments.map(appointment => ({
      id: appointment._id,
      serviceId: (appointment.service as any)?._id,
      serviceName: (appointment.service as any)?.name,
      servicePrice: (appointment.service as any)?.price,
      serviceDuration: (appointment.service as any)?.duration,
      serviceCategory: (appointment.service as any)?.category,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      user: appointment.user ? {
        id: (appointment.user as any)._id,
        name: (appointment.user as any).name,
        email: (appointment.user as any).email,
        phone: (appointment.user as any).phone
      } : null,
      notes: appointment.message,
      createdAt: appointment.createdAt,
      updatedAt: appointment.updatedAt
    }));

    sendSuccess(res, 'Appointments retrieved successfully', {
      appointments: formattedAppointments,
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalItems: total,
        itemsPerPage: limitNum,
        hasNextPage: pageNum < totalPages,
        hasPrevPage: pageNum > 1
      }
    });
  } catch (error) {
    console.error('Get all appointments error:', error);
    sendError(res, (error as Error).message);
  }
});

// Note: Availability checking has been moved to /api/v2/availability endpoints
// This endpoint is deprecated and will be removed in a future version

// Note: Availability endpoints have been moved to /api/v2/availability
// This endpoint is deprecated and will be removed in a future version

// POST /api/v2/appointments - Create new appointment (public endpoint)
router.post('/', async (req: Request, res: Response) => {
  try {
    const { serviceId, date, time, customerInfo, addOns = [], userId, paymentProof } = req.body;

    // Validate required fields including payment proof
    if (!serviceId || !date || !time || !customerInfo) {
      sendError(res, 'Service, date, time, and customer info are required', undefined, 400);
      return;
    }

    // Payment proof is required
    if (!paymentProof || !paymentProof.url) {
      sendError(res, 'Payment proof is required to create an appointment', undefined, 400);
      return;
    }

    // Decode time if it's URL encoded
    const decodedTime = decodeURIComponent(time).replace(':undefined', '');

    // Find or create user based on email
    let user = null;
    if (userId) {
      user = await User.findById(userId);
    } else if (customerInfo.email) {
      // Check if user exists by email
      user = await User.findOne({ email: customerInfo.email.toLowerCase() });

      // If user doesn't exist, create one automatically without password
      if (!user) {
        try {
          // Create user without bcrypt - they can set password later if needed
          user = await User.create({
            name: `${customerInfo.firstName} ${customerInfo.lastName}`,
            firstName: customerInfo.firstName,
            lastName: customerInfo.lastName,
            email: customerInfo.email.toLowerCase(),
            phone: customerInfo.phone || '',
            role: 'user',
            isVerified: false,
            // Use a placeholder password that will require reset
            password: 'TEMP_PASSWORD_REQUIRES_RESET_' + Date.now()
          });
          console.log(`Auto-created user for email: ${customerInfo.email}`);
        } catch (createError) {
          console.error('Error creating user:', createError);
          // Continue without user if creation fails
          user = null;
        }
      }
    }

    // Check if service exists
    const service = await Service.findById(serviceId);
    if (!service || !service.isActive) {
      sendError(res, 'Service not found or not available', undefined, 404);
      return;
    }

    // Parse the date and combine with time for a complete datetime
    // Use UTC to avoid timezone issues
    const appointmentDate = new Date(date + 'T00:00:00.000Z');

    // If time is provided, try to parse and combine with date
    let combinedDateTime = appointmentDate;
    if (decodedTime) {
      try {
        // Parse time (handle various formats)
        const timeMatch = decodedTime.match(/(\d{1,2}):(\d{2})\s*(AM|PM)?/i);
        if (timeMatch) {
          let hours = parseInt(timeMatch[1]);
          const minutes = parseInt(timeMatch[2]);
          const period = timeMatch[3];

          // Convert to 24-hour format if needed
          if (period) {
            if (period.toUpperCase() === 'PM' && hours !== 12) {
              hours += 12;
            } else if (period.toUpperCase() === 'AM' && hours === 12) {
              hours = 0;
            }
          }

          // Set the time on the date using UTC
          combinedDateTime = new Date(appointmentDate);
          combinedDateTime.setUTCHours(hours, minutes, 0, 0);
        }
      } catch (timeError) {
        console.error('Error parsing time:', timeError);
        // Use original date if time parsing fails
      }
    }

    // Check admin availability using the availability service
    const isAvailable = await availabilityService.isTimeSlotAvailable(
      format(appointmentDate, 'yyyy-MM-dd'),
      decodedTime
    );

    if (!isAvailable) {
      sendError(res, `The selected time slot (${decodedTime}) is not available. Please choose another time.`, undefined, 409);
      return;
    }

    // Additional check: Verify no existing appointment at this exact time slot
    const existingAppointment = await Appointment.findOne({
      date: appointmentDate,
      time: decodedTime,
      status: { $in: ['pending', 'confirmed'] }
    });

    if (existingAppointment) {
      sendError(res, 'This time slot is already booked. Please select a different time.', undefined, 409);
      return;
    }

    // Calculate total price (for response only, not stored in appointment)
    let totalPrice = service.price;
    const addOnTotal = addOns.reduce((sum: number, addOn: any) => sum + (addOn.price || 0), 0);
    totalPrice += addOnTotal;

    // Prepare appointment data
    const appointmentData: any = {
      user: user ? user._id : null, // Use found/created user or null for guest bookings
      service: service._id,
      date: combinedDateTime, // Use combined date and time
      time: decodedTime,
      status: 'pending',
      type: 'service',
      customerInfo: {
        name: `${customerInfo.firstName} ${customerInfo.lastName}`,
        email: customerInfo.email,
        phone: customerInfo.phone
      },
      message: req.body.notes || '',
      totalPrice: totalPrice
    };

    // Add payment proof (required)
    console.log('Adding payment proof to appointment:', paymentProof);
    appointmentData.paymentProofs = [{
      id: new Date().getTime().toString(),
      amount: paymentProof.amount || totalPrice,
      paymentMethod: paymentProof.paymentMethod || 'unknown',
      proofImage: paymentProof.url,
      status: 'pending',
      notes: paymentProof.notes || 'Payment proof uploaded during booking',
      createdAt: new Date()
    }];
    appointmentData.paymentStatus = 'pending';
    console.log('Payment proof added to appointment data');

    // Create appointment with error handling for duplicates
    try {
      const appointment = await Appointment.create(appointmentData);

      // Populate service data for response
      await appointment.populate('service', 'name price duration category');

      const responseData = {
        id: appointment._id,
        userId: user ? user._id : null,
        serviceId: (appointment.service as any)._id,
        serviceName: (appointment.service as any).name,
        servicePrice: (appointment.service as any).price,
        date: appointment.date,
        time: appointment.time,
        status: appointment.status,
        customerInfo: appointment.customerInfo,
        totalPrice: totalPrice,
        addOns: addOns,
        notes: appointment.message,
        paymentProofs: appointment.paymentProofs || [],
        paymentStatus: appointment.paymentStatus || 'pending',
        createdAt: appointment.createdAt
      };

      sendCreated(res, 'Appointment created successfully', responseData);
    } catch (createError: any) {
      // Handle duplicate key error specifically
      if (createError.code === 11000 && createError.message.includes('date_1_time_1_status_1')) {
        sendError(res, 'Time slot is already booked. Please select a different time.', undefined, 409);
        return;
      }
      throw createError; // Re-throw if it's a different error
    }
  } catch (error) {
    console.error('Create appointment error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/appointments/user - Get user dashboard data (public endpoint)
router.get('/user', async (req: Request, res: Response) => {
  try {
    const { email, userId } = req.query;

    if (!email && !userId) {
      sendError(res, 'Email or userId query parameter is required. Usage: ?email=<EMAIL> or ?userId=123', undefined, 400);
      return;
    }

    // Find user by email or userId
    let user = null;
    if (userId) {
      user = await User.findById(userId);
    } else if (email) {
      user = await User.findOne({ email: email.toString().toLowerCase() });
    }

    if (!user) {
      sendError(res, 'User not found', undefined, 404);
      return;
    }

    // Get all user appointments with full population
    const appointments = await Appointment.find({ user: user._id })
      .populate('service', 'name duration price category description')
      .populate('user', 'name email phone')
      .sort({ date: -1, time: -1 });

    // Get appointment statistics
    const appointmentStats = {
      total: appointments.length,
      pending: appointments.filter(apt => apt.status === 'pending').length,
      confirmed: appointments.filter(apt => apt.status === 'confirmed').length,
      completed: appointments.filter(apt => apt.status === 'completed').length,
      cancelled: appointments.filter(apt => apt.status === 'cancelled').length
    };

    // Get recent appointments (last 5)
    const recentAppointments = appointments.slice(0, 5).map(appointment => ({
      id: appointment._id,
      serviceId: (appointment.service as any)?._id,
      serviceName: (appointment.service as any)?.name,
      servicePrice: (appointment.service as any)?.price,
      serviceDuration: (appointment.service as any)?.duration,
      serviceCategory: (appointment.service as any)?.category,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      notes: appointment.message,
      createdAt: appointment.createdAt,
      updatedAt: appointment.updatedAt
    }));

    // Get upcoming appointments
    const now = new Date();
    const upcomingAppointments = appointments
      .filter(apt => {
        const appointmentDate = new Date(apt.date);
        return appointmentDate >= now && (apt.status === 'pending' || apt.status === 'confirmed');
      })
      .slice(0, 3)
      .map(appointment => ({
        id: appointment._id,
        serviceId: (appointment.service as any)?._id,
        serviceName: (appointment.service as any)?.name,
        servicePrice: (appointment.service as any)?.price,
        serviceDuration: (appointment.service as any)?.duration,
        date: appointment.date,
        time: appointment.time,
        status: appointment.status,
        notes: appointment.message
      }));

    // Calculate total spent
    const totalSpent = appointments
      .filter(apt => apt.status === 'completed')
      .reduce((sum, apt) => {
        const servicePrice = (apt.service as any)?.price || 0;
        return sum + servicePrice;
      }, 0);

    // Get favorite services (most booked)
    const serviceBookings = appointments.reduce((acc: any, apt) => {
      const serviceId = (apt.service as any)?._id?.toString();
      const serviceName = (apt.service as any)?.name;
      if (serviceId && serviceName) {
        if (!acc[serviceId]) {
          acc[serviceId] = {
            id: serviceId,
            name: serviceName,
            count: 0,
            price: (apt.service as any)?.price || 0,
            category: (apt.service as any)?.category || 'General'
          };
        }
        acc[serviceId].count++;
      }
      return acc;
    }, {});

    const favoriteServices = Object.values(serviceBookings)
      .sort((a: any, b: any) => b.count - a.count)
      .slice(0, 3);

    // Remove password from user object
    const userResponse = user.toObject();
    const { password, ...userWithoutPassword } = userResponse;

    // Send comprehensive dashboard data
    sendSuccess(res, 'User dashboard data retrieved successfully', {
      user: userWithoutPassword,
      appointments: {
        all: appointments.map(appointment => ({
          id: appointment._id,
          serviceId: (appointment.service as any)?._id,
          serviceName: (appointment.service as any)?.name,
          servicePrice: (appointment.service as any)?.price,
          serviceDuration: (appointment.service as any)?.duration,
          serviceCategory: (appointment.service as any)?.category,
          date: appointment.date,
          time: appointment.time,
          status: appointment.status,
          customerInfo: appointment.customerInfo,
          notes: appointment.message,
          createdAt: appointment.createdAt,
          updatedAt: appointment.updatedAt
        })),
        recent: recentAppointments,
        upcoming: upcomingAppointments
      },
      statistics: {
        appointments: appointmentStats,
        totalSpent,
        favoriteServices,
        memberSince: user.createdAt,
        lastActivity: user.updatedAt
      }
    });
  } catch (error) {
    console.error('Get user dashboard data error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/appointments/my - Get user's appointments
router.get('/my', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const appointments = await Appointment.find({ user: req.user._id })
      .populate('service', 'name price duration category')
      .sort({ date: -1, time: -1 });

    const formattedAppointments = appointments.map(appointment => ({
      id: appointment._id,
      serviceId: (appointment.service as any)._id,
      serviceName: (appointment.service as any).name,
      servicePrice: (appointment.service as any).price,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      totalPrice: (appointment.service as any).price, // Use service price as total
      notes: appointment.message,
      createdAt: appointment.createdAt
    }));

    sendSuccess(res, 'User appointments retrieved successfully', formattedAppointments);
  } catch (error) {
    console.error('Get user appointments error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/appointments/:id - Update appointment
router.put('/:id', authenticate, validate(mongoIdValidation()), async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const { id } = req.params;
    const { date, time, customerInfo, notes } = req.body;

    const appointment = await Appointment.findOne({
      _id: id,
      user: req.user._id
    });

    if (!appointment) {
      sendError(res, 'Appointment not found', undefined, 404);
      return;
    }

    // Only allow updates to pending appointments
    if (appointment.status !== 'pending') {
      sendError(res, 'Cannot update confirmed or completed appointments', undefined, 400);
      return;
    }

    // Update fields
    if (date) appointment.date = new Date(date);
    if (time) appointment.time = time;
    if (customerInfo) {
      appointment.customerInfo = {
        name: customerInfo.firstName && customerInfo.lastName
          ? `${customerInfo.firstName} ${customerInfo.lastName}`
          : appointment.customerInfo.name,
        email: customerInfo.email || appointment.customerInfo.email,
        phone: customerInfo.phone || appointment.customerInfo.phone
      };
    }
    if (notes !== undefined) appointment.message = notes;

    await appointment.save();
    await appointment.populate('service', 'name price duration category');

    const responseData = {
      id: appointment._id,
      serviceId: (appointment.service as any)._id,
      serviceName: (appointment.service as any).name,
      servicePrice: (appointment.service as any).price,
      date: appointment.date,
      time: appointment.time,
      status: appointment.status,
      customerInfo: appointment.customerInfo,
      totalPrice: (appointment.service as any).price,
      notes: appointment.message,
      updatedAt: appointment.updatedAt
    };

    sendSuccess(res, 'Appointment updated successfully', responseData);
  } catch (error) {
    console.error('Update appointment error:', error);
    sendError(res, (error as Error).message);
  }
});

// DELETE /api/v2/appointments/:id - Cancel appointment
router.delete('/:id', authenticate, validate(mongoIdValidation()), async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const { id } = req.params;

    const appointment = await Appointment.findOne({
      _id: id,
      user: req.user._id
    });

    if (!appointment) {
      sendError(res, 'Appointment not found', undefined, 404);
      return;
    }

    appointment.status = 'cancelled';
    await appointment.save();

    sendSuccess(res, 'Appointment cancelled successfully', { id: appointment._id });
  } catch (error) {
    console.error('Cancel appointment error:', error);
    sendError(res, (error as Error).message);
  }
});

// POST /api/v2/appointments/:id/payment-proof - Add payment proof to existing appointment
router.post('/:id/payment-proof',
  UploadController.cloudinaryUploadSingle('proofImage'),
  async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const { amount, paymentMethod, notes, email } = req.body;

      if (!req.file) {
        sendError(res, 'Payment proof image is required', undefined, 400);
        return;
      }

      // Find the appointment
      const appointment = await Appointment.findById(id);
      if (!appointment) {
        sendError(res, 'Appointment not found', undefined, 404);
        return;
      }

      // Verify email matches appointment customer
      if (appointment.customerInfo.email.toLowerCase() !== email.toLowerCase()) {
        sendError(res, 'Email does not match appointment customer', undefined, 403);
        return;
      }

      // Add payment proof to appointment
      const paymentProof = {
        id: new Date().getTime().toString(),
        amount: parseFloat(amount) || appointment.totalPrice,
        paymentMethod: paymentMethod || 'unknown',
        proofImage: (req as any).file.path, // Cloudinary URL
        status: 'pending' as const,
        notes: notes || '',
        createdAt: new Date()
      };

      appointment.paymentProofs.push(paymentProof);
      await appointment.save();

      // Populate service data for response
      await appointment.populate('service', 'name price duration category');

      sendSuccess(res, 'Payment proof added successfully', {
        appointmentId: appointment._id,
        paymentProof: paymentProof,
        appointment: {
          id: appointment._id,
          serviceName: (appointment.service as any).name,
          date: appointment.date,
          time: appointment.time,
          status: appointment.status,
          customerInfo: appointment.customerInfo,
          totalPrice: appointment.totalPrice,
          paymentProofs: appointment.paymentProofs
        }
      });
    } catch (error) {
      console.error('Add payment proof error:', error);
      sendError(res, (error as Error).message);
    }
  }
);

// GET /api/v2/appointments/user/:userId - Get appointments for a specific user
router.get('/user/:userId', async (req: Request, res: Response) => {
  try {
    const { userId } = req.params;
    const { status, page = 1, limit = 20 } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Build filter query
    const filter: any = { user: userId };
    if (status && status !== 'all') {
      filter.status = status;
    }

    const appointments = await Appointment.find(filter)
      .populate('service', 'name category description price duration')
      .populate('user', 'firstName lastName email')
      .sort({ date: -1, time: -1 })
      .skip(skip)
      .limit(limitNum);

    const total = await Appointment.countDocuments(filter);

    sendSuccess(res, 'User appointments retrieved successfully', {
      appointments,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Get user appointments error:', error);
    sendError(res, (error as Error).message);
  }
});

export default router;
