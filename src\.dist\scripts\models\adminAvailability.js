"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalAvailabilitySettings = exports.AdminAvailability = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const TimeSlotSchema = new mongoose_1.Schema({
    time: {
        type: String,
        required: true,
        validate: {
            validator: function (v) {
                return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
            },
            message: 'Time must be in HH:MM format'
        }
    },
    isAvailable: {
        type: Boolean,
        default: true
    },
    reason: {
        type: String,
        maxlength: 200
    }
}, { _id: false });
const AdminAvailabilitySchema = new mongoose_1.Schema({
    date: {
        type: Date,
        required: true,
        index: true,
        validate: {
            validator: function (v) {
                return v >= new Date(new Date().setHours(0, 0, 0, 0));
            },
            message: 'Date cannot be in the past'
        }
    },
    timeSlots: [TimeSlotSchema],
    isFullDayUnavailable: {
        type: Boolean,
        default: false
    },
    reason: {
        type: String,
        maxlength: 500
    },
    createdBy: {
        type: mongoose_1.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true
});
const GlobalAvailabilitySettingsSchema = new mongoose_1.Schema({
    defaultTimeSlots: [TimeSlotSchema],
    businessHours: {
        start: {
            type: String,
            required: true,
            default: '00:00',
            validate: {
                validator: function (v) {
                    return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
                },
                message: 'Start time must be in HH:MM format'
            }
        },
        end: {
            type: String,
            required: true,
            default: '04:00',
            validate: {
                validator: function (v) {
                    return /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(v);
                },
                message: 'End time must be in HH:MM format'
            }
        }
    },
    workingDays: {
        type: [Number],
        default: [1, 2, 3, 4, 5], // Monday to Friday
        validate: {
            validator: function (v) {
                return v.every(day => day >= 0 && day <= 6);
            },
            message: 'Working days must be between 0 (Sunday) and 6 (Saturday)'
        }
    },
    slotDuration: {
        type: Number,
        default: 30,
        min: 15,
        max: 120
    },
    isGloballyUnavailable: {
        type: Boolean,
        default: false
    },
    globalUnavailabilityReason: {
        type: String,
        maxlength: 500
    }
}, {
    timestamps: true
});
// Ensure unique date entries
AdminAvailabilitySchema.index({ date: 1 }, { unique: true });
// Ensure only one global settings document
GlobalAvailabilitySettingsSchema.index({}, { unique: true });
// Static methods for AdminAvailability
AdminAvailabilitySchema.statics.generateDefaultTimeSlots = function (businessHours, slotDuration = 30) {
    const slots = [];
    const [startHour, startMinute] = businessHours.start.split(':').map(Number);
    const [endHour, endMinute] = businessHours.end.split(':').map(Number);
    const startTime = startHour * 60 + startMinute;
    let endTime = endHour * 60 + endMinute;
    // Handle overnight hours (e.g., 12 AM to 4 AM)
    if (endTime <= startTime) {
        endTime += 24 * 60; // Add 24 hours for next day
    }
    for (let time = startTime; time < endTime; time += slotDuration) {
        const hour = Math.floor(time / 60) % 24; // Wrap around 24 hours
        const minute = time % 60;
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        slots.push({
            time: timeString,
            isAvailable: true
        });
    }
    return slots;
};
exports.AdminAvailability = mongoose_1.default.model('AdminAvailability', AdminAvailabilitySchema);
exports.GlobalAvailabilitySettings = mongoose_1.default.model('GlobalAvailabilitySettings', GlobalAvailabilitySettingsSchema);
exports.default = exports.AdminAvailability;
