/**
 * Utility functions for consistent time formatting across the application
 */

/**
 * Convert 24-hour time format to 12-hour AM/PM format
 * @param time24 - Time in 24-hour format (e.g., "14:30", "09:00")
 * @returns Time in 12-hour format (e.g., "2:30 PM", "9:00 AM")
 */
export function formatTo12Hour(time24: string): string {
  if (!time24 || typeof time24 !== 'string') {
    return '';
  }

  // Handle time with seconds (e.g., "14:30:00")
  const timeParts = time24.split(':');
  if (timeParts.length < 2) {
    return time24; // Return original if invalid format
  }

  const hours = parseInt(timeParts[0], 10);
  const minutes = parseInt(timeParts[1], 10);

  if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
    return time24; // Return original if invalid
  }

  const period = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
  const displayMinutes = minutes.toString().padStart(2, '0');

  return `${displayHours}:${displayMinutes} ${period}`;
}

/**
 * Convert 12-hour AM/PM format to 24-hour format
 * @param time12 - Time in 12-hour format (e.g., "2:30 PM", "9:00 AM")
 * @returns Time in 24-hour format (e.g., "14:30", "09:00")
 */
export function formatTo24Hour(time12: string): string {
  if (!time12 || typeof time12 !== 'string') {
    return '';
  }

  const timeRegex = /^(\d{1,2}):(\d{2})\s*(AM|PM)$/i;
  const match = time12.trim().match(timeRegex);

  if (!match) {
    return time12; // Return original if invalid format
  }

  let hours = parseInt(match[1], 10);
  const minutes = parseInt(match[2], 10);
  const period = match[3].toUpperCase();

  if (isNaN(hours) || isNaN(minutes) || hours < 1 || hours > 12 || minutes < 0 || minutes > 59) {
    return time12; // Return original if invalid
  }

  // Convert to 24-hour format
  if (period === 'AM') {
    if (hours === 12) hours = 0;
  } else { // PM
    if (hours !== 12) hours += 12;
  }

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

/**
 * Format a Date object to 12-hour time string
 * @param date - Date object
 * @returns Time in 12-hour format (e.g., "2:30 PM")
 */
export function formatDateTo12Hour(date: Date): string {
  if (!date || !(date instanceof Date) || isNaN(date.getTime())) {
    return '';
  }

  const hours = date.getHours();
  const minutes = date.getMinutes();
  const period = hours >= 12 ? 'PM' : 'AM';
  const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
  const displayMinutes = minutes.toString().padStart(2, '0');

  return `${displayHours}:${displayMinutes} ${period}`;
}

/**
 * Format appointment time for display
 * @param time - Time string in various formats
 * @returns Formatted time in 12-hour format
 */
export function formatAppointmentTime(time: string | Date): string {
  if (!time) return '';

  if (time instanceof Date) {
    return formatDateTo12Hour(time);
  }

  if (typeof time === 'string') {
    // If already in 12-hour format, return as is
    if (time.includes('AM') || time.includes('PM')) {
      return time;
    }
    // Convert from 24-hour format
    return formatTo12Hour(time);
  }

  return '';
}

/**
 * Get current time in 12-hour format
 * @returns Current time in 12-hour format
 */
export function getCurrentTime12Hour(): string {
  return formatDateTo12Hour(new Date());
}

/**
 * Check if a time string is in 12-hour format
 * @param time - Time string to check
 * @returns True if in 12-hour format, false otherwise
 */
export function is12HourFormat(time: string): boolean {
  if (!time || typeof time !== 'string') return false;
  return /\s*(AM|PM)$/i.test(time.trim());
}

/**
 * Generate time slots in 12-hour format
 * @param startHour - Starting hour (0-23)
 * @param endHour - Ending hour (0-23)
 * @param intervalMinutes - Interval in minutes (default: 30)
 * @returns Array of time slots in 12-hour format
 */
export function generateTimeSlots12Hour(
  startHour: number = 0,
  endHour: number = 4,
  intervalMinutes: number = 30
): string[] {
  const slots: string[] = [];
  
  let currentHour = startHour;
  let currentMinute = 0;

  while (currentHour < endHour || (currentHour === endHour && currentMinute === 0)) {
    const time24 = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}`;
    slots.push(formatTo12Hour(time24));

    currentMinute += intervalMinutes;
    if (currentMinute >= 60) {
      currentHour += Math.floor(currentMinute / 60);
      currentMinute = currentMinute % 60;
    }
  }

  return slots;
}

/**
 * Sort time slots in chronological order (handles overnight times)
 * @param times - Array of time strings in 12-hour format
 * @returns Sorted array of time strings
 */
export function sortTimeSlots12Hour(times: string[]): string[] {
  return times.sort((a, b) => {
    const time24A = formatTo24Hour(a);
    const time24B = formatTo24Hour(b);
    
    const [hoursA, minutesA] = time24A.split(':').map(Number);
    const [hoursB, minutesB] = time24B.split(':').map(Number);
    
    const totalMinutesA = hoursA * 60 + minutesA;
    const totalMinutesB = hoursB * 60 + minutesB;
    
    return totalMinutesA - totalMinutesB;
  });
}

/**
 * Format time range for display
 * @param startTime - Start time string
 * @param endTime - End time string
 * @returns Formatted time range (e.g., "9:00 AM - 5:00 PM")
 */
export function formatTimeRange(startTime: string, endTime: string): string {
  const start = formatAppointmentTime(startTime);
  const end = formatAppointmentTime(endTime);
  
  if (!start || !end) return '';
  
  return `${start} - ${end}`;
}
