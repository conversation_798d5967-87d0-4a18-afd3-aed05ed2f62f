"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminReminderController = void 0;
const appointmentReminderService_1 = require("../services/appointmentReminderService");
const response_1 = require("../utils/response");
class AdminReminderController {
    // Get reminder statistics for admin dashboard
    static async getReminderStats(req, res) {
        try {
            const stats = appointmentReminderService_1.appointmentReminderService.getReminderStats();
            (0, response_1.sendSuccess)(res, 'Reminder statistics retrieved successfully', stats);
        }
        catch (error) {
            console.error('Get reminder stats error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Get reminder logs for admin dashboard
    static async getReminderLogs(req, res) {
        try {
            const { limit = 50 } = req.query;
            const logs = appointmentReminderService_1.appointmentReminderService.getReminderLogs(Number(limit));
            (0, response_1.sendSuccess)(res, 'Reminder logs retrieved successfully', {
                logs,
                total: logs.length
            });
        }
        catch (error) {
            console.error('Get reminder logs error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Send test reminder for specific appointment
    static async sendTestReminder(req, res) {
        try {
            const { appointmentId } = req.params;
            const { reminderType } = req.body;
            if (!appointmentId) {
                (0, response_1.sendError)(res, 'Appointment ID is required', undefined, 400);
                return;
            }
            if (!reminderType || !['48h', '24h'].includes(reminderType)) {
                (0, response_1.sendError)(res, 'Valid reminder type (48h or 24h) is required', undefined, 400);
                return;
            }
            const success = await appointmentReminderService_1.appointmentReminderService.sendTestReminder(appointmentId, reminderType);
            if (success) {
                (0, response_1.sendSuccess)(res, `Test ${reminderType} reminder sent successfully`);
            }
            else {
                (0, response_1.sendError)(res, 'Failed to send test reminder', undefined, 500);
            }
        }
        catch (error) {
            console.error('Send test reminder error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Trigger manual reminder check (for testing)
    static async triggerReminderCheck(req, res) {
        try {
            const { type } = req.body;
            if (!type || !['48h', '24h', 'both'].includes(type)) {
                (0, response_1.sendError)(res, 'Valid reminder type (48h, 24h, or both) is required', undefined, 400);
                return;
            }
            let results = {};
            if (type === '48h' || type === 'both') {
                await appointmentReminderService_1.appointmentReminderService.send48HourReminders();
                results.fortyEightHour = 'completed';
            }
            if (type === '24h' || type === 'both') {
                await appointmentReminderService_1.appointmentReminderService.send24HourReminders();
                results.twentyFourHour = 'completed';
            }
            (0, response_1.sendSuccess)(res, 'Manual reminder check completed', results);
        }
        catch (error) {
            console.error('Trigger reminder check error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
    // Get reminder dashboard data
    static async getReminderDashboard(req, res) {
        try {
            const stats = appointmentReminderService_1.appointmentReminderService.getReminderStats();
            const recentLogs = appointmentReminderService_1.appointmentReminderService.getReminderLogs(20);
            const dashboardData = {
                stats,
                recentActivity: recentLogs,
                summary: {
                    totalReminders: stats.total48h + stats.total24h,
                    successRate: stats.total48h + stats.total24h > 0
                        ? Math.round(((stats.successful48h + stats.successful24h) / (stats.total48h + stats.total24h)) * 100)
                        : 0,
                    last24Hours: stats.last24Hours,
                    failedReminders: stats.failed48h + stats.failed24h
                }
            };
            (0, response_1.sendSuccess)(res, 'Reminder dashboard data retrieved successfully', dashboardData);
        }
        catch (error) {
            console.error('Get reminder dashboard error:', error);
            (0, response_1.sendError)(res, error.message);
        }
    }
}
exports.AdminReminderController = AdminReminderController;
