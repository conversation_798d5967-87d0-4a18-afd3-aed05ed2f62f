const mongoose = require('mongoose');
require('dotenv').config();

async function fixDuplicateIndexes() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/microlocs');
    console.log('Connected to MongoDB');

    const db = mongoose.connection.db;
    
    // Drop problematic unique indexes from reviews collection
    try {
      console.log('Checking existing indexes on reviews collection...');
      const reviewIndexes = await db.collection('reviews').indexes();
      console.log('Current review indexes:', reviewIndexes.map(idx => ({ name: idx.name, key: idx.key, unique: idx.unique })));

      // Drop unique indexes that prevent multiple reviews
      const indexesToDrop = [
        'user_1_product_1',
        'user_1_service_1', 
        'appointment_1'
      ];

      for (const indexName of indexesToDrop) {
        try {
          await db.collection('reviews').dropIndex(indexName);
          console.log(`✅ Dropped index: ${indexName}`);
        } catch (error) {
          if (error.code === 27) {
            console.log(`ℹ️  Index ${indexName} does not exist (already dropped)`);
          } else {
            console.log(`⚠️  Could not drop index ${indexName}:`, error.message);
          }
        }
      }

      // Create new non-unique indexes for performance
      console.log('Creating new non-unique indexes...');
      
      try {
        await db.collection('reviews').createIndex({ appointment: 1 }, { sparse: true });
        console.log('✅ Created non-unique appointment index');
      } catch (error) {
        console.log('ℹ️  Appointment index already exists or error:', error.message);
      }

    } catch (error) {
      console.error('Error handling review indexes:', error);
    }

    // Check appointments collection for any problematic unique indexes
    try {
      console.log('\nChecking existing indexes on appointments collection...');
      const appointmentIndexes = await db.collection('appointments').indexes();
      console.log('Current appointment indexes:', appointmentIndexes.map(idx => ({ name: idx.name, key: idx.key, unique: idx.unique })));

      // Look for any unique indexes that might cause duplicates
      const uniqueIndexes = appointmentIndexes.filter(idx => idx.unique && idx.name !== '_id_');
      if (uniqueIndexes.length > 0) {
        console.log('Found unique indexes that might cause duplicates:', uniqueIndexes);
        
        for (const index of uniqueIndexes) {
          try {
            await db.collection('appointments').dropIndex(index.name);
            console.log(`✅ Dropped unique appointment index: ${index.name}`);
          } catch (error) {
            console.log(`⚠️  Could not drop appointment index ${index.name}:`, error.message);
          }
        }
      }

    } catch (error) {
      console.error('Error handling appointment indexes:', error);
    }

    console.log('\n✅ Index cleanup completed successfully!');
    console.log('Users can now create multiple reviews and appointments should not duplicate.');

  } catch (error) {
    console.error('Error fixing indexes:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the fix
fixDuplicateIndexes().catch(console.error);
