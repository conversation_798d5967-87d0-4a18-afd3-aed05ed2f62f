import { useState, useEffect } from 'react';
import { serviceAPI, type Service, type ServiceFilters as ServiceFiltersType } from '../../utils/serviceAPI';
import LoadingSpinner from '../LoadingSpinner';
import ServiceModal from './ServiceModal';
import CategoryModal from './CategoryModal';
import ServiceList from './ServiceList';
import ServiceFilters from './ServiceFilters';
import Pagination from '../Pagination';

interface ServiceManagementProps {
  onError?: (message: string) => void;
  onSuccess?: (message: string) => void;
}

export default function ServiceManagement({ onError, onSuccess }: ServiceManagementProps) {
  // State management
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Modal states
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  
  // Filter and pagination states
  const [filters, setFilters] = useState<ServiceFiltersType>({
    page: 1,
    limit: 20,
    search: '',
    category: '',
    isActive: undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });
  
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  });
  
  // Selection states
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
  
  // Load services data
  const loadServices = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await serviceAPI.getAdminServices(filters);
      
      if (response.success) {
        setServices(response.data.services);
        setPagination(response.data.pagination);
      } else {
        throw new Error('Failed to load services');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load services';
      setError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };
  
  // Load categories
  const loadCategories = async () => {
    try {
      const response = await serviceAPI.getCategories();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (err) {
      console.error('Failed to load categories:', err);
    }
  };
  
  // Initial load
  useEffect(() => {
    loadServices();
    loadCategories();
  }, [filters]);
  
  // Handle service operations
  const handleCreateService = async (serviceData: any) => {
    try {
      const response = await serviceAPI.createService(serviceData);
      if (response.success) {
        onSuccess?.('Service created successfully');
        setShowServiceModal(false);
        loadServices();
        loadCategories(); // Reload categories in case a new one was added
      } else {
        throw new Error('Failed to create service');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create service';
      onError?.(errorMessage);
    }
  };
  
  const handleUpdateService = async (serviceData: any) => {
    if (!editingService) return;
    
    try {
      const response = await serviceAPI.updateService(editingService.id, serviceData);
      if (response.success) {
        onSuccess?.('Service updated successfully');
        setShowServiceModal(false);
        setEditingService(null);
        loadServices();
        loadCategories(); // Reload categories in case category was changed
      } else {
        throw new Error('Failed to update service');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update service';
      onError?.(errorMessage);
    }
  };
  
  const handleDeleteService = async (serviceId: string) => {
    if (!confirm('Are you sure you want to delete this service?')) return;
    
    try {
      const response = await serviceAPI.deleteService(serviceId);
      if (response.success) {
        onSuccess?.('Service deleted successfully');
        loadServices();
        setSelectedServices(prev => prev.filter(id => id !== serviceId));
      } else {
        throw new Error('Failed to delete service');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete service';
      onError?.(errorMessage);
    }
  };
  
  // Handle bulk operations
  const handleBulkDelete = async () => {
    if (selectedServices.length === 0) return;
    
    if (!confirm(`Are you sure you want to delete ${selectedServices.length} service(s)?`)) return;
    
    try {
      await Promise.all(selectedServices.map(id => serviceAPI.deleteService(id)));
      onSuccess?.(`${selectedServices.length} service(s) deleted successfully`);
      setSelectedServices([]);
      loadServices();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete services';
      onError?.(errorMessage);
    }
  };
  
  const handleBulkStatusChange = async (isActive: boolean) => {
    if (selectedServices.length === 0) return;
    
    try {
      await Promise.all(
        selectedServices.map(id => 
          serviceAPI.updateService(id, { isActive })
        )
      );
      onSuccess?.(`${selectedServices.length} service(s) ${isActive ? 'activated' : 'deactivated'} successfully`);
      setSelectedServices([]);
      loadServices();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update services';
      onError?.(errorMessage);
    }
  };
  
  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<ServiceFiltersType>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters,
      page: 1 // Reset to first page when filters change
    }));
  };
  
  // Handle pagination
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };
  
  // Handle service editing
  const handleEditService = (service: Service) => {
    setEditingService(service);
    setShowServiceModal(true);
  };
  
  // Handle service modal close
  const handleServiceModalClose = () => {
    setShowServiceModal(false);
    setEditingService(null);
  };
  
  // Handle service save
  const handleServiceSave = (serviceData: any) => {
    if (editingService) {
      handleUpdateService(serviceData);
    } else {
      handleCreateService(serviceData);
    }
  };
  
  // Computed values
  const hasSelectedServices = selectedServices.length > 0;
  const allServicesSelected = services.length > 0 && selectedServices.length === services.length;
  const someServicesSelected = selectedServices.length > 0 && selectedServices.length < services.length;
  
  return (
    <div className="service-management">
      {/* Header */}
      <div className="page-header">
        <div>
          <h1>Service Management</h1>
          <p className="text-gray-600">Manage your service offerings, categories, and pricing</p>
        </div>
        <div className="header-actions">
          <button
            onClick={() => setShowCategoryModal(true)}
            className="btn btn-outline"
          >
            Manage Categories
          </button>
          <button
            onClick={() => setShowServiceModal(true)}
            className="btn btn-primary"
          >
            Add Service
          </button>
        </div>
      </div>
      
      {/* Error Display */}
      {error && (
        <div className="error-message">
          <span>{error}</span>
          <button 
            onClick={() => setError(null)}
            className="error-close"
          >
            ×
          </button>
        </div>
      )}
      
      {/* Filters */}
      <ServiceFilters
        filters={filters}
        categories={categories}
        onFilterChange={handleFilterChange}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
      />
      
      {/* Bulk Actions */}
      {hasSelectedServices && (
        <div className="bulk-actions">
          <div className="selected-count">
            {selectedServices.length} service{selectedServices.length !== 1 ? 's' : ''} selected
          </div>
          <div className="bulk-actions-buttons">
            <button
              onClick={() => handleBulkStatusChange(true)}
              className="btn btn-sm btn-secondary"
            >
              Activate
            </button>
            <button
              onClick={() => handleBulkStatusChange(false)}
              className="btn btn-sm btn-secondary"
            >
              Deactivate
            </button>
            <button
              onClick={handleBulkDelete}
              className="btn btn-sm btn-danger"
            >
              Delete
            </button>
          </div>
        </div>
      )}
      
      {/* Loading State */}
      {loading && (
        <div className="loading-container">
          <LoadingSpinner />
          <p>Loading services...</p>
        </div>
      )}
      
      {/* Services List */}
      {!loading && (
        <ServiceList
          services={services}
          viewMode={viewMode}
          selectedServices={selectedServices}
          onSelectionChange={setSelectedServices}
          onEdit={handleEditService}
          onDelete={handleDeleteService}
          allSelected={allServicesSelected}
          someSelected={someServicesSelected}
          searchTerm={filters.search || ''}
        />
      )}
      
      {/* Pagination */}
      {!loading && services.length > 0 && (
        <Pagination
          currentPage={pagination.page}
          totalPages={pagination.pages}
          totalItems={pagination.total}
          itemsPerPage={pagination.limit}
          onPageChange={handlePageChange}
        />
      )}
      
      {/* Empty State */}
      {!loading && services.length === 0 && !error && (
        <div className="empty-state">
          <div className="empty-icon">📋</div>
          <h3>No services found</h3>
          <p>Get started by creating your first service offering.</p>
          <button
            onClick={() => setShowServiceModal(true)}
            className="btn btn-primary"
          >
            Create Service
          </button>
        </div>
      )}
      
      {/* Modals */}
      {showServiceModal && (
        <ServiceModal
          isOpen={showServiceModal}
          onClose={handleServiceModalClose}
          onSave={handleServiceSave}
          service={editingService}
          categories={categories}
        />
      )}
      
      {showCategoryModal && (
        <CategoryModal
          isOpen={showCategoryModal}
          onClose={() => setShowCategoryModal(false)}
          categories={categories}
          onCategoriesChange={loadCategories}
          onError={onError}
          onSuccess={onSuccess}
        />
      )}
    </div>
  );
}
