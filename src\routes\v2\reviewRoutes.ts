import { Router, Request, Response } from 'express';
import { Review } from '../../models/Review';
import { Appointment } from '../../models/Appointment';
import { Service } from '../../models/Service';
import { sendSuccess, sendError, sendCreated } from '../../utils/response';
import { authenticate } from '../../middleware/auth';
import { AuthenticatedRequest } from '../../types';

const router = Router();

// GET /api/v2/reviews/appointment/:appointmentId - Get review for specific appointment
router.get('/appointment/:appointmentId', async (req: Request, res: Response) => {
  try {
    const { appointmentId } = req.params;

    const review = await Review.findOne({ appointment: appointmentId })
      .populate('user', 'firstName lastName')
      .populate('service', 'name')
      .populate('appointment', 'date time status');

    if (!review) {
      sendError(res, 'Review not found', undefined, 404);
      return;
    }

    sendSuccess(res, 'Review retrieved successfully', review);
  } catch (error) {
    console.error('Get appointment review error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/reviews/service/:serviceId - Get all reviews for a service (public endpoint)
router.get('/service/:serviceId', async (req: Request, res: Response) => {
  try {
    const { serviceId } = req.params;
    const { page = 1, limit = 10, status = 'approved' } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    const query: any = { service: serviceId };
    if (status !== 'all') {
      query.status = status;
    }

    const reviews = await Review.find(query)
      .populate('user', 'firstName lastName')
      .populate('appointment', 'date time')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    const total = await Review.countDocuments(query);

    // Calculate average rating
    const ratingStats = await Review.aggregate([
      { $match: { service: serviceId, status: 'approved' } },
      {
        $group: {
          _id: null,
          averageRating: { $avg: '$rating' },
          totalReviews: { $sum: 1 },
          ratingDistribution: {
            $push: '$rating'
          }
        }
      }
    ]);

    const stats = ratingStats[0] || { averageRating: 0, totalReviews: 0, ratingDistribution: [] };

    sendSuccess(res, 'Service reviews retrieved successfully', {
      reviews,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      },
      stats: {
        averageRating: Math.round(stats.averageRating * 10) / 10,
        totalReviews: stats.totalReviews,
        ratingDistribution: stats.ratingDistribution
      }
    });
  } catch (error) {
    console.error('Get service reviews error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/reviews - Get all approved reviews (PUBLIC)
router.get('/', async (req: Request, res: Response) => {
  try {
    const { page = 1, limit = 10, service, rating } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // Build query
    const query: any = { status: 'approved' };
    if (service) query.service = service;
    if (rating) query.rating = parseInt(rating as string);

    // Get reviews
    const reviews = await Review.find(query)
      .populate('user', 'firstName lastName')
      .populate('service', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    // Get total count
    const total = await Review.countDocuments(query);

    const responseData = {
      reviews,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    };

    sendSuccess(res, 'Reviews retrieved successfully', responseData);
  } catch (error) {
    console.error('Get reviews error:', error);
    sendError(res, (error as Error).message);
  }
});

// POST /api/v2/reviews/service - Create a new service review (guest reviews)
router.post('/service', async (req: Request, res: Response) => {
  try {
    const { serviceId, rating, title, comment, customerName, customerEmail } = req.body;

    // Validate required fields
    if (!serviceId || !rating || !customerName) {
      sendError(res, 'Service ID, rating, and customer name are required', undefined, 400);
      return;
    }

    if (rating < 1 || rating > 5) {
      sendError(res, 'Rating must be between 1 and 5', undefined, 400);
      return;
    }

    // Validate email format if provided
    if (customerEmail) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(customerEmail)) {
        sendError(res, 'Please provide a valid email address', undefined, 400);
        return;
      }
    }

    // Check if service exists
    const service = await Service.findById(serviceId);
    if (!service) {
      sendError(res, 'Service not found', undefined, 404);
      return;
    }

    // Create review
    const reviewData: any = {
      service: serviceId,
      rating,
      title: title || '',
      comment: comment || '',
      customerName: customerName.trim(),
      customerEmail: customerEmail.trim().toLowerCase(),
      status: 'pending', // All service reviews need approval
      isVerifiedPurchase: false // Since it's not tied to an appointment
    };

    const review = await Review.create(reviewData);
    const populatedReview = await Review.findById(review._id)
      .populate('service', 'name');

    sendCreated(res, 'Review submitted successfully and is pending approval', populatedReview);
  } catch (error) {
    console.error('Create service review error:', error);
    sendError(res, (error as Error).message);
  }
});

// POST /api/v2/reviews/service-review - Create a new service review (not tied to appointment)
router.post('/service-review', async (req: Request, res: Response) => {
  try {
    const { serviceId, rating, title, comment, customerName } = req.body;

    // Validate required fields
    if (!serviceId || !rating || !customerName) {
      sendError(res, 'Service ID, rating, and customer name are required', undefined, 400);
      return;
    }

    if (rating < 1 || rating > 5) {
      sendError(res, 'Rating must be between 1 and 5', undefined, 400);
      return;
    }

    // Check if service exists
    const service = await Service.findById(serviceId);
    if (!service) {
      sendError(res, 'Service not found', undefined, 404);
      return;
    }

    // Create review
    const reviewData: any = {
      service: serviceId,
      rating,
      title: title || '',
      comment: comment || '',
      customerName: customerName.trim(),
      status: 'pending', // All service reviews need approval
      isVerifiedPurchase: false // Since it's not tied to an appointment
    };

    const review = await Review.create(reviewData);
    const populatedReview = await Review.findById(review._id)
      .populate('service', 'name');

    sendCreated(res, 'Review submitted successfully and is pending approval', populatedReview);
  } catch (error) {
    console.error('Create service review error:', error);
    sendError(res, (error as Error).message);
  }
});

// POST /api/v2/reviews/public - Create a public review (no authentication required)
router.post('/public', async (req: Request, res: Response) => {
  try {
    const { serviceId, rating, title, comment, customerName, customerEmail, appointmentId } = req.body;

    // Validate required fields
    if (!serviceId || !rating || !customerName) {
      sendError(res, 'Service ID, rating, and customer name are required', undefined, 400);
      return;
    }

    if (rating < 1 || rating > 5) {
      sendError(res, 'Rating must be between 1 and 5', undefined, 400);
      return;
    }

    // Validate email format if provided
    if (customerEmail) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(customerEmail)) {
        sendError(res, 'Please provide a valid email address', undefined, 400);
        return;
      }
    }

    // Check if service exists
    const service = await Service.findById(serviceId);
    if (!service) {
      sendError(res, 'Service not found', undefined, 404);
      return;
    }

    // Check if appointment exists (optional)
    let isVerifiedPurchase = false;
    if (appointmentId) {
      const appointment = await Appointment.findById(appointmentId);
      if (appointment && appointment.status === 'completed') {
        isVerifiedPurchase = true;
      }
    }

    // Create review
    const reviewData: any = {
      service: serviceId,
      rating,
      title: title || '',
      comment: comment || '',
      customerName: customerName.trim(),
      customerEmail: customerEmail.trim().toLowerCase(),
      status: 'pending', // All public reviews need approval
      isVerifiedPurchase,
      helpfulVotes: 0
    };

    if (appointmentId) {
      reviewData.appointment = appointmentId;
    }

    const review = await Review.create(reviewData);
    const populatedReview = await Review.findById(review._id)
      .populate('service', 'name')
      .populate('appointment', 'date time');

    sendCreated(res, 'Review submitted successfully and is pending approval', populatedReview);
  } catch (error) {
    console.error('Create public review error:', error);
    sendError(res, (error as Error).message);
  }
});

// POST /api/v2/reviews - Create a new review (appointment-based)
router.post('/', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const { appointmentId, rating, title, comment, customerName } = req.body;

    // Validate required fields
    if (!appointmentId || !rating) {
      sendError(res, 'Appointment ID and rating are required', undefined, 400);
      return;
    }

    if (rating < 1 || rating > 5) {
      sendError(res, 'Rating must be between 1 and 5', undefined, 400);
      return;
    }

    // Check if appointment exists and is completed
    const appointment = await Appointment.findById(appointmentId).populate('service');
    if (!appointment) {
      sendError(res, 'Appointment not found', undefined, 404);
      return;
    }

    if (appointment.status !== 'completed') {
      sendError(res, 'Can only review completed appointments', undefined, 400);
      return;
    }

    // Check if the appointment belongs to the authenticated user
    if (appointment.user.toString() !== req.user._id.toString()) {
      sendError(res, 'You can only review your own appointments', undefined, 403);
      return;
    }

    // Allow multiple reviews for the same appointment
    // Users should be able to update their experience over time

    // Create review - for appointment-based reviews, only set appointment field
    const reviewData: any = {
      user: req.user._id,
      appointment: appointmentId,
      // Don't set service field for appointment-based reviews to avoid validation conflict
      rating,
      title: title || '',
      comment: comment || '',
      customerName: customerName || appointment.customerInfo.name || `${req.user.firstName} ${req.user.lastName}`,
      status: 'pending', // Reviews need approval
      isVerifiedPurchase: true // Since it's from a completed appointment
    };

    const review = await Review.create(reviewData);
    const populatedReview = await Review.findById(review._id)
      .populate('user', 'firstName lastName')
      .populate({
        path: 'appointment',
        select: 'date time service',
        populate: {
          path: 'service',
          select: 'name category'
        }
      });

    sendCreated(res, 'Review created successfully', populatedReview);
  } catch (error) {
    console.error('Create review error:', error);
    sendError(res, (error as Error).message);
  }
});

// PUT /api/v2/reviews/:reviewId - Update a review
router.put('/:reviewId', async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;
    const { rating, title, comment } = req.body;

    const review = await Review.findById(reviewId);
    if (!review) {
      sendError(res, 'Review not found', undefined, 404);
      return;
    }

    // Update fields
    if (rating !== undefined) {
      if (rating < 1 || rating > 5) {
        sendError(res, 'Rating must be between 1 and 5', undefined, 400);
        return;
      }
      review.rating = rating;
    }

    if (title !== undefined) review.title = title;
    if (comment !== undefined) review.comment = comment;

    await review.save();

    const updatedReview = await Review.findById(reviewId)
      .populate('user', 'firstName lastName')
      .populate('service', 'name')
      .populate('appointment', 'date time');

    sendSuccess(res, 'Review updated successfully', updatedReview);
  } catch (error) {
    console.error('Update review error:', error);
    sendError(res, (error as Error).message);
  }
});

// DELETE /api/v2/reviews/:reviewId - Delete a review
router.delete('/:reviewId', async (req: Request, res: Response) => {
  try {
    const { reviewId } = req.params;

    const review = await Review.findByIdAndDelete(reviewId);
    if (!review) {
      sendError(res, 'Review not found', undefined, 404);
      return;
    }

    sendSuccess(res, 'Review deleted successfully', { deletedId: reviewId });
  } catch (error) {
    console.error('Delete review error:', error);
    sendError(res, (error as Error).message);
  }
});

// GET /api/v2/reviews/user/:userId - Get all reviews by a specific user
router.get('/user/:userId', authenticate, async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (!req.user) {
      sendError(res, 'Authentication required', undefined, 401);
      return;
    }

    const { userId } = req.params;

    // Users can only view their own reviews unless they're admin
    if (req.user.role !== 'admin' && req.user._id.toString() !== userId) {
      sendError(res, 'You can only view your own reviews', undefined, 403);
      return;
    }
    const { page = 1, limit = 10 } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    const reviews = await Review.find({ user: userId })
      .populate('service', 'name category')
      .populate('appointment', 'date time status')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    const total = await Review.countDocuments({ user: userId });

    sendSuccess(res, 'User reviews retrieved successfully', {
      reviews,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        pages: Math.ceil(total / limitNum)
      }
    });
  } catch (error) {
    console.error('Get user reviews error:', error);
    sendError(res, (error as Error).message);
  }
});

export default router;
