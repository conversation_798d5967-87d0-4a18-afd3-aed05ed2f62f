import { useState, useEffect } from 'react';
import { API_CONFIG } from '../../utils/config';

interface ConsultationSectionProps {
  onBookService: (service: any) => void;
  showDetails: boolean;
  onToggleDetails: () => void;
}

interface Service {
  _id: string;
  name: string;
  price: number;
  duration: number;
  category: string;
  description: string;
  isActive: boolean;
  type: string;
}

export default function ConsultationSection({
  onBookService,
  showDetails,
  onToggleDetails
}: ConsultationSectionProps) {
  const [consultationService, setConsultationService] = useState<Service | null>(null);
  const [brandingData, setBrandingData] = useState<any>(null);

  useEffect(() => {
    fetchConsultationService();
    loadBrandingData();
  }, []);

  const loadBrandingData = async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/branding`);
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data) {
          setBrandingData(data.data);
        }
      }
    } catch (error) {
      console.error('Error loading branding data:', error);
    }
  };

  const fetchConsultationService = async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/services`);
      if (response.ok) {
        const data = await response.json();
        // The API returns grouped services, so we need to flatten them
        const groupedServices = data.data || {};
        const flatServices: Service[] = [];

        // Flatten the grouped services into a single array
        Object.values(groupedServices).forEach((categoryServices: any) => {
          if (Array.isArray(categoryServices)) {
            categoryServices.forEach((service: any) => {
              flatServices.push({
                _id: service.id,
                name: service.name,
                price: parseFloat(service.price),
                duration: service.duration,
                category: service.category,
                description: service.description,
                isActive: true,
                type: service.type || 'service'
              });
            });
          }
        });

        // Find the consultation service
        const consultation = flatServices.find((service: Service) =>
          service.type === 'consultation' ||
          service.name.toLowerCase().includes('consultation')
        );
        setConsultationService(consultation || null);
      }
    } catch (error) {
      console.error('Error fetching consultation service:', error);
    }
  };
  const consultation = brandingData?.home?.consultation;

  return (
    <div className="consultation-section">
      <div className="consultation-card">
        <h3 className="consultation-subtitle">
          {consultation?.subtitle || 'READY TO START YOUR LOC JOURNEY?'}
        </h3>

        <p className="consultation-intro">
          {consultation?.intro || 'To begin, you\'ll need to book a consultation which costs $30 (non-refundable).'}
        </p>

        <p className="consultation-options-title">
          {consultation?.optionsTitle || 'We offer two options to suit your needs:'}
        </p>

        <div className="consultation-options">
          {consultation?.options?.map((option: any, index: number) => (
            <div key={option._id || index} className="consultation-option">
              <h4>{option.title}</h4>
            </div>
          )) || (
            <>
              <div className="consultation-option">
                <h4>1. IN-PERSON CONSULTATION</h4>
              </div>
              <div className="consultation-option">
                <h4>2. VIDEO CALL CONSULTATION</h4>
              </div>
            </>
          )}
        </div>

        <p className="consultation-duration">
          {consultation?.duration || 'Each session will be 30 minutes'}
        </p>

        <div className="consultation-benefits">
          {consultation?.benefits?.map((benefit: string, index: number) => (
            <div key={index} className="benefit-item">{benefit}</div>
          )) || (
            <>
              <div className="benefit-item">✓ Curating your hair goals</div>
              <div className="benefit-item">✓ Discussing your hair type and its condition</div>
              <div className="benefit-item">✓ Recommending the best methods for your loc journey to thrive</div>
            </>
          )}
        </div>

        <p className="consultation-note">
          {consultation?.note || 'Keep in mind, this consultation is mandatory for better understanding your hair journey.'}
        </p>

        <div className="consultation-actions">
          <button
            className="consultation-cta"
            onClick={() => consultationService && onBookService({
              id: consultationService._id,
              name: consultationService.name,
              price: consultationService.price,
              duration: consultationService.duration,
              category: consultationService.category,
              description: consultationService.description
            })}
            disabled={!consultationService}
          >
            {consultationService ?
              (consultation?.ctaText || 'BOOK YOUR CONSULTATION NOW') :
              'Loading...'
            }
          </button>
          <button
            className="read-more-btn"
            onClick={onToggleDetails}
          >
            {showDetails ?
              (consultation?.readLessText || 'Read Less') :
              (consultation?.readMoreText || 'Read More')
            }
          </button>
        </div>

        {showDetails && (
          <div className="consultation-details">
            <div className="consultation-prep">
              <h4>{consultation?.preparation?.title || 'HOW TO BE PREPARED FOR YOUR CONSULTATION'}</h4>
              <ul>
                {consultation?.preparation?.items?.map((item: string, index: number) => (
                  <li key={index} dangerouslySetInnerHTML={{ __html: item }} />
                )) || (
                  <>
                    <li><strong>Hair Must Be Undone</strong> - Please ensure your hair is completely taken down (no braids, twists, or extensions)</li>
                    <li><strong>Clean & Product-Free</strong> - Wash your hair and avoid using oils, creams, or gels</li>
                    <li><strong>Natural State</strong> - Your hair should be free from heat styling and in its natural texture</li>
                    <li><strong>Good Lighting</strong> - Be in a well-lit area so your hair can be seen clearly</li>
                    <li><strong>Clear Video or Photos</strong> - If the consultation is virtual, send a clear video or pictures of your hair before the appointment</li>
                    <li><strong>Questions Ready</strong> - Write down any questions or concerns you may have about the service</li>
                  </>
                )}
              </ul>
            </div>

            <div className="installation-prep">
              <h4>{consultation?.installation?.title || 'Things to Do Before Your Installation Appointment'}</h4>
              <ul>
                {consultation?.installation?.items?.map((item: string, index: number) => (
                  <li key={index} dangerouslySetInnerHTML={{ __html: item }} />
                )) || (
                  <>
                    <li><strong>Wash your hair</strong> - the day before or the morning of your appointment</li>
                    <li><strong>Skip blowouts, flat irons, or curling irons</strong></li>
                    <li><strong>Detangle gently</strong> - Make sure your hair is free from knots</li>
                    <li><strong>Avoid products</strong> - avoid gels, oils, or creams in your hair</li>
                    <li><strong>Plan for comfort</strong> - Bring your headphones, snacks, laptop, or a drink - appointments take time!</li>
                    <li><strong>Arrive on time</strong> - Being on time helps us start and finish smoothly</li>
                  </>
                )}
              </ul>
            </div>

            <div className="transfer-consultation">
              <h4>{consultation?.transfer?.title || 'CONSULTATION FOR TRANSFERRED CLIENTS'}</h4>
              <p>{consultation?.transfer?.content || 'A consultation is required for all transferred clients.'}</p>
              <p>{consultation?.transfer?.fee || 'The fee is $30 (non-refundable)'}</p>
              <p>{consultation?.transfer?.description || 'This process allows us to better understand your loc journey and guide you through the next steps for a smooth, continuous experience.'}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
