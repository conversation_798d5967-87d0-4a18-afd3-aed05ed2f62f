/**
 * Image constants and URLs for the application
 */

// Cloudinary hosted images
export const CLOUDINARY_IMAGES = {
  MICROLOC: 'https://res.cloudinary.com/djeddsyoq/image/upload/v1756121239/microlocs/services/pfwsyyhuev0egq0ns4gy.jpg'
} as const

// Fallback images
export const FALLBACK_IMAGES = {
  MICROLOC_EXTERNAL: 'https://images.pexels.com/photos/19143443/pexels-photo-19143443/free-photo-of-black-girl-with-blonde-locs-standing-outside-a-store-at-night.jpeg?auto=compress&cs=tinysrgb&w=600'
} as const

/**
 * Get microloc image sources in order of preference
 * @param localAsset - Local imported asset as fallback
 * @returns Array of image sources to try
 */
export function getMicrolocImageSources(localAsset?: string): string[] {
  const sources: string[] = [
    CLOUDINARY_IMAGES.MICROLOC, // Primary: Cloudinary CDN
  ]

  if (localAsset) {
    sources.push(localAsset) // Secondary: Local imported asset
  }

  sources.push(
    '/microloc.jpg', // Tertiary: Public folder
    './microloc.jpg', // Quaternary: Relative path
    `${window.location.origin}/microloc.jpg`, // Quinary: Absolute URL
    FALLBACK_IMAGES.MICROLOC_EXTERNAL // Final: External fallback
  )

  return sources
}

/**
 * Create error handler for microloc image with progressive fallbacks
 * @param sources - Array of image sources to try
 * @param currentIndex - Current source index
 * @param setCurrentIndex - Function to update current index
 * @param setCurrentSrc - Function to update current source
 * @param setImageState - Function to update image state
 * @returns Error handler function
 */
export function createMicrolocImageErrorHandler(
  sources: string[],
  currentIndex: number,
  setCurrentIndex: (index: number) => void,
  setCurrentSrc: (src: string) => void,
  setImageState: (state: 'loading' | 'loaded' | 'error') => void
) {
  return () => {
    console.error(`Microloc image failed to load from: ${sources[currentIndex]}`)
    
    // Try next fallback source
    if (currentIndex < sources.length - 1) {
      const nextIndex = currentIndex + 1
      const nextSrc = sources[nextIndex]
      console.log(`Trying fallback image ${nextIndex + 1}/${sources.length}: ${nextSrc}`)
      setCurrentIndex(nextIndex)
      setCurrentSrc(nextSrc)
      setImageState('loading')
    } else {
      console.warn('All microloc image sources failed')
      setImageState('error')
    }
  }
}
