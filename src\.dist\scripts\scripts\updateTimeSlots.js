"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateTimeSlots = updateTimeSlots;
const database_1 = require("../config/database");
const adminAvailability_1 = require("../models/adminAvailability");
const mongoose_1 = __importDefault(require("mongoose"));
/**
 * Script to update existing time slots from old schedule (9 AM - 5 PM)
 * to new overnight schedule (12 AM - 4 AM)
 */
// Generate new overnight time slots (12 AM to 4 AM)
function generateOvernightTimeSlots() {
    const timeSlots = [];
    // Generate slots from 12 AM (00:00) to 4 AM (04:00) in 30-minute intervals
    for (let hour = 0; hour < 4; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
            timeSlots.push({
                time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
                isAvailable: true
            });
        }
    }
    return timeSlots;
}
// Update global availability settings
async function updateGlobalSettings() {
    console.log('🔧 Updating global availability settings...');
    try {
        const GlobalAvailabilitySettings = mongoose_1.default.model('GlobalAvailabilitySettings');
        const newSettings = {
            defaultTimeSlots: generateOvernightTimeSlots(),
            businessHours: {
                start: '00:00',
                end: '04:00'
            },
            workingDays: [1, 2, 3, 4, 5, 6, 0], // All days for overnight work
            slotDuration: 30,
            isGloballyUnavailable: false
        };
        // Update or create global settings
        const result = await GlobalAvailabilitySettings.findOneAndUpdate({}, newSettings, { upsert: true, new: true });
        console.log('✅ Global availability settings updated successfully');
        return result;
    }
    catch (error) {
        console.error('❌ Error updating global settings:', error);
        throw error;
    }
}
// Update existing admin availability records
async function updateExistingAvailability() {
    console.log('🔧 Updating existing admin availability records...');
    try {
        const newTimeSlots = generateOvernightTimeSlots();
        // Get all existing availability records
        const existingRecords = await adminAvailability_1.AdminAvailability.find({});
        console.log(`📊 Found ${existingRecords.length} existing availability records`);
        if (existingRecords.length === 0) {
            console.log('ℹ️  No existing records to update');
            return;
        }
        // Update all records with new time slots
        const updateResult = await adminAvailability_1.AdminAvailability.updateMany({}, {
            $set: {
                timeSlots: newTimeSlots
            }
        });
        console.log(`✅ Updated ${updateResult.modifiedCount} availability records`);
        return updateResult;
    }
    catch (error) {
        console.error('❌ Error updating existing availability:', error);
        throw error;
    }
}
// Create sample availability for next 30 days
async function createSampleAvailability() {
    console.log('🔧 Creating sample availability for next 30 days...');
    try {
        const timeSlots = generateOvernightTimeSlots();
        const today = new Date();
        const operations = [];
        // Create availability for next 30 days
        for (let i = 0; i < 30; i++) {
            const date = new Date(today);
            date.setDate(today.getDate() + i);
            date.setHours(0, 0, 0, 0); // Reset time to start of day
            operations.push({
                updateOne: {
                    filter: { date },
                    update: {
                        date,
                        timeSlots,
                        isFullDayUnavailable: false
                    },
                    upsert: true
                }
            });
        }
        const result = await adminAvailability_1.AdminAvailability.bulkWrite(operations);
        console.log(`✅ Created/updated availability for next 30 days`);
        console.log(`   - Modified: ${result.modifiedCount}`);
        console.log(`   - Upserted: ${result.upsertedCount}`);
        return result;
    }
    catch (error) {
        console.error('❌ Error creating sample availability:', error);
        throw error;
    }
}
// Main update function
async function updateTimeSlots() {
    try {
        console.log('🚀 Starting time slot update to overnight schedule...');
        console.log('==========================================');
        await (0, database_1.connectDatabase)();
        // Step 1: Update global settings
        await updateGlobalSettings();
        // Step 2: Update existing availability records
        await updateExistingAvailability();
        // Step 3: Create sample availability for next 30 days
        await createSampleAvailability();
        console.log('==========================================');
        console.log('🎉 Time slot update completed successfully!');
        console.log('📅 New schedule: 12:00 AM - 4:00 AM (overnight)');
        console.log('⏰ 30-minute intervals');
        console.log('==========================================');
    }
    catch (error) {
        console.error('❌ Time slot update failed:', error);
        process.exit(1);
    }
    finally {
        await (0, database_1.disconnectDatabase)();
    }
}
// Run the script if executed directly
if (require.main === module) {
    updateTimeSlots();
}
