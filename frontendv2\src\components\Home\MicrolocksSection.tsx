import { useState, useEffect } from 'react';
import { API_CONFIG } from '../../utils/config';

export default function MicrolocksSection() {
  const [brandingData, setBrandingData] = useState<any>(null);

  useEffect(() => {
    const loadBrandingData = async () => {
      try {
        const response = await fetch(`${API_CONFIG.BASE_URL}/branding`);
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data) {
            setBrandingData(data.data);
          }
        }
      } catch (error) {
        console.error('Error loading branding data:', error);
      }
    };

    loadBrandingData();
  }, []);

  const microlocks = brandingData?.home?.microlocks;

  return (
    <div className="info-card microlocks-card">
      <h3 className="info-title">
        {microlocks?.title || 'Microlocks'}
      </h3>
      <div className="info-content">
        {microlocks?.content ? (
          <div dangerouslySetInnerHTML={{ __html: microlocks.content }} />
        ) : (
          <p>Full payment is required to secure all Microlock establishment appointments. This must be paid at the time of scheduling.</p>
        )}

        <p className="methods-title">
          {microlocks?.methodsTitle || '3 STARTING METHODS TO CHOOSE FROM:'}
        </p>

        <ul className="methods-list">
          {microlocks?.methods?.map((method: string, index: number) => (
            <li key={index}>{method}</li>
          )) || (
            <>
              <li>1 - INTERLOCKS</li>
              <li>2 - TWO STRAND TWIST</li>
              <li>3 - BRAID LOCS</li>
            </>
          )}
        </ul>

        {microlocks?.durationText && (
          <p>{microlocks.durationText}</p>
        )}
      </div>
    </div>
  )
}
