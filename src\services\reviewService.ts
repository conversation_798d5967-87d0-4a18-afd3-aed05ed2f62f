import { Review } from '../models/Review';
import { Order } from '../models/Order';
import { Appointment } from '../models/Appointment';
import { IReview } from '../types';
import mongoose from 'mongoose';

export class ReviewService {
  /**
   * Create a new review
   */
  static async createReview(reviewData: {
    user: string;
    product?: string;
    service?: string;
    rating: number;
    title: string;
    comment: string;
  }): Promise<IReview> {
    // Remove duplicate check - allow users to create multiple reviews
    // Users should be able to review the same item multiple times as their experience may change

    // Check if this is a verified purchase
    let isVerifiedPurchase = false;
    if (reviewData.product) {
      // Check if user has purchased this product
      const order = await Order.findOne({
        user: reviewData.user,
        'items.product': reviewData.product,
        status: 'delivered'
      });
      isVerifiedPurchase = !!order;
    } else if (reviewData.service) {
      // Check if user has booked this service
      const appointment = await Appointment.findOne({
        user: reviewData.user,
        service: reviewData.service,
        status: 'completed'
      });
      isVerifiedPurchase = !!appointment;
    }

    const review = new Review({
      ...reviewData,
      isVerifiedPurchase,
      status: 'pending' // All reviews start as pending for admin approval
    });

    return await review.save();
  }

  /**
   * Get reviews for a product or service
   */
  static async getReviews(params: {
    product?: string;
    service?: string;
    status?: string;
    page?: number;
    limit?: number;
  }) {
    const { product, service, status = 'approved', page = 1, limit = 10 } = params;
    
    const query: any = { status };
    if (product) query.product = product;
    if (service) query.service = service;

    const skip = (page - 1) * limit;

    const [reviews, total] = await Promise.all([
      Review.find(query)
        .populate('user', 'firstName lastName name')
        .populate('product', 'name')
        .populate('service', 'name')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      Review.countDocuments(query)
    ]);

    return {
      reviews,
      total,
      page,
      totalPages: Math.ceil(total / limit),
      hasMore: page * limit < total
    };
  }

  /**
   * Get review statistics for a product or service
   */
  static async getReviewStats(productId?: string, serviceId?: string) {
    const query: any = { status: 'approved' };
    if (productId) query.product = productId;
    if (serviceId) query.service = serviceId;

    const stats = await Review.aggregate([
      { $match: query },
      {
        $group: {
          _id: null,
          totalReviews: { $sum: 1 },
          averageRating: { $avg: '$rating' },
          ratingDistribution: {
            $push: '$rating'
          }
        }
      }
    ]);

    if (stats.length === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
      };
    }

    const { totalReviews, averageRating, ratingDistribution } = stats[0];
    
    // Calculate rating distribution
    const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
    ratingDistribution.forEach((rating: number) => {
      distribution[rating as keyof typeof distribution]++;
    });

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10, // Round to 1 decimal
      ratingDistribution: distribution
    };
  }

  /**
   * Update review status (admin only)
   */
  static async updateReviewStatus(reviewId: string, status: 'approved' | 'rejected'): Promise<IReview | null> {
    return await Review.findByIdAndUpdate(
      reviewId,
      { status },
      { new: true }
    ).populate('user', 'firstName lastName name')
     .populate('product', 'name')
     .populate('service', 'name');
  }

  /**
   * Delete a review
   */
  static async deleteReview(reviewId: string, userId?: string): Promise<boolean> {
    const query: any = { _id: reviewId };
    if (userId) query.user = userId; // Users can only delete their own reviews

    const result = await Review.deleteOne(query);
    return result.deletedCount > 0;
  }

  /**
   * Get user's reviews
   */
  static async getUserReviews(userId: string, page = 1, limit = 10) {
    const skip = (page - 1) * limit;

    const [reviews, total] = await Promise.all([
      Review.find({ user: userId })
        .populate('product', 'name image')
        .populate('service', 'name')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      Review.countDocuments({ user: userId })
    ]);

    return {
      reviews,
      total,
      page,
      totalPages: Math.ceil(total / limit),
      hasMore: page * limit < total
    };
  }

  /**
   * Get all reviews for admin management
   */
  static async getAllReviews(params: {
    status?: string;
    page?: number;
    limit?: number;
    search?: string;
  }) {
    const { status, page = 1, limit = 20, search } = params;
    
    const query: any = {};
    if (status && status !== 'all') query.status = status;
    
    // Add search functionality
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { comment: { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (page - 1) * limit;

    const [reviews, total] = await Promise.all([
      Review.find(query)
        .populate('user', 'firstName lastName name email')
        .populate('product', 'name')
        .populate('service', 'name')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      Review.countDocuments(query)
    ]);

    return {
      reviews,
      total,
      page,
      totalPages: Math.ceil(total / limit),
      hasMore: page * limit < total
    };
  }

  /**
   * Update a review (user can edit their own review)
   */
  static async updateReview(
    reviewId: string, 
    userId: string, 
    updateData: {
      rating?: number;
      title?: string;
      comment?: string;
    }
  ): Promise<IReview | null> {
    return await Review.findOneAndUpdate(
      { _id: reviewId, user: userId },
      { ...updateData, status: 'pending' }, // Reset to pending when edited
      { new: true }
    ).populate('user', 'firstName lastName name')
     .populate('product', 'name')
     .populate('service', 'name');
  }
}
