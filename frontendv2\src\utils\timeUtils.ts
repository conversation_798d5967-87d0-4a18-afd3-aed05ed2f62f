// Centralized time formatting utilities for consistent AM/PM display across the application

/**
 * Formats a time string to 12-hour format with AM/PM
 * @param timeString - Time in various formats (HH:MM, HH:MM:SS, or full date string)
 * @returns Formatted time string in 12-hour format with AM/PM
 */
export const formatTimeTo12Hour = (timeString: string | undefined | null): string => {
  // Handle null/undefined cases
  if (!timeString) {
    return 'Time TBD';
  }

  // Handle URL encoded time - decode multiple times if needed
  let decodedTime = timeString;
  try {
    // Decode until no more encoding is found
    while (decodedTime.includes('%')) {
      const newDecoded = decodeURIComponent(decodedTime);
      if (newDecoded === decodedTime) break; // No more decoding needed
      decodedTime = newDecoded;
    }
  } catch (error) {
    console.warn('Error decoding time:', error);
    decodedTime = timeString;
  }

  // If already in 12-hour format, return as is
  if (decodedTime.includes('AM') || decodedTime.includes('PM')) {
    return decodedTime;
  }

  try {
    // Handle different time formats
    let time: Date;
    if (decodedTime.includes(':')) {
      // Format: "HH:MM" or "HH:MM:SS"
      const [hours, minutes] = decodedTime.split(':');
      time = new Date();
      time.setHours(parseInt(hours), parseInt(minutes), 0, 0);
    } else {
      // Try to parse as a full date/time string
      time = new Date(decodedTime);
    }

    // Format to 12-hour format
    return time.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  } catch (error) {
    console.error('Error formatting time:', error);
    return decodedTime; // Return original if formatting fails
  }
};

/**
 * Converts 12-hour time format to 24-hour format for backend storage
 * @param time12h - Time in 12-hour format (e.g., "2:30 PM")
 * @returns Time in 24-hour format (e.g., "14:30")
 */
export const convertTo24Hour = (time12h: string): string => {
  if (!time12h) return '';

  const [time, modifier] = time12h.split(' ');
  let [hours, minutes] = time.split(':');

  if (hours === '12') {
    hours = '00';
  }

  if (modifier === 'PM') {
    hours = (parseInt(hours, 10) + 12).toString();
  }

  return `${hours.padStart(2, '0')}:${minutes}`;
};

/**
 * Formats a date to a readable string
 * @param dateString - Date string or Date object
 * @returns Formatted date string
 */
export const formatDate = (dateString: string | Date): string => {
  if (!dateString) return 'Date not set';
  
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { 
    weekday: 'short', 
    month: 'short', 
    day: 'numeric',
    year: 'numeric'
  });
};

/**
 * Formats duration from hours to readable format
 * @param hours - Duration in hours
 * @returns Formatted duration string
 */
export const formatDuration = (hours: number): string => {
  if (!hours) return 'Duration not set';

  if (hours === 1) {
    return '1 hour';
  } else if (hours % 1 === 0) {
    return `${hours} hours`;
  } else {
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);
    if (wholeHours === 0) {
      return `${minutes} minutes`;
    }
    return `${wholeHours}h ${minutes}m`;
  }
};

/**
 * Formats time slots for display (ensures 12-hour format)
 * @param time - Time string in any format
 * @returns Formatted time slot in 12-hour format
 */
export const formatTimeSlot = (time: string): string => {
  // If time is already in 12h format, return as is
  if (time.includes('AM') || time.includes('PM')) {
    return time;
  }

  // Convert 24h format to 12h format
  const [hours, minutes] = time.split(':');
  const hour = parseInt(hours);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour % 12 || 12;
  return `${displayHour}:${minutes} ${ampm}`;
};
