import cron from 'node-cron';
import { Appointment } from '../models';
import { emailService } from './emailService';
import { addDays, addHours, format, isAfter, isBefore } from 'date-fns';

interface ReminderLog {
  appointmentId: string;
  reminderType: '48h' | '24h';
  sentAt: Date;
  status: 'sent' | 'failed';
  error?: string;
}

class AppointmentReminderService {
  private reminderLogs: ReminderLog[] = [];
  private isInitialized = false;

  // Initialize the reminder service with cron jobs
  init(): void {
    if (this.isInitialized) {
      console.log('Appointment reminder service already initialized');
      return;
    }

    console.log('Initializing appointment reminder service...');

    // Run 48-hour reminders every hour
    cron.schedule('0 * * * *', () => {
      this.send48HourReminders();
    });

    // Run 24-hour reminders every hour
    cron.schedule('0 * * * *', () => {
      this.send24HourReminders();
    });

    // Clean up old logs daily at midnight
    cron.schedule('0 0 * * *', () => {
      this.cleanupOldLogs();
    });

    this.isInitialized = true;
    console.log('Appointment reminder service initialized successfully');
  }

  // Send 48-hour reminders
  async send48HourReminders(): Promise<void> {
    try {
      console.log('Checking for 48-hour reminders...');
      
      const targetDate = addHours(new Date(), 48);
      const startWindow = addHours(targetDate, -1); // 1 hour before target
      const endWindow = addHours(targetDate, 1);   // 1 hour after target

      const appointments = await Appointment.find({
        date: {
          $gte: startWindow,
          $lte: endWindow
        },
        status: { $in: ['pending', 'confirmed'] }
      }).populate('user', 'email firstName lastName name');

      console.log(`Found ${appointments.length} appointments for 48-hour reminders`);

      for (const appointment of appointments) {
        // Check if 48-hour reminder already sent
        const alreadySent = this.reminderLogs.some(log => 
          log.appointmentId === appointment._id.toString() && 
          log.reminderType === '48h' && 
          log.status === 'sent'
        );

        if (alreadySent) {
          continue;
        }

        try {
          if (appointment.user && (appointment.user as any).email) {
            await emailService.send48HourReminder(appointment.user as any, appointment as any);
            
            this.logReminder({
              appointmentId: appointment._id.toString(),
              reminderType: '48h',
              sentAt: new Date(),
              status: 'sent'
            });

            console.log(`48-hour reminder sent to ${(appointment.user as any).email}`);
          }
        } catch (error) {
          console.error(`Failed to send 48-hour reminder for appointment ${appointment._id}:`, error);
          
          this.logReminder({
            appointmentId: appointment._id.toString(),
            reminderType: '48h',
            sentAt: new Date(),
            status: 'failed',
            error: (error as Error).message
          });
        }
      }

      console.log('48-hour reminders completed');
    } catch (error) {
      console.error('Error sending 48-hour reminders:', error);
    }
  }

  // Send 24-hour reminders
  async send24HourReminders(): Promise<void> {
    try {
      console.log('Checking for 24-hour reminders...');
      
      const targetDate = addHours(new Date(), 24);
      const startWindow = addHours(targetDate, -1); // 1 hour before target
      const endWindow = addHours(targetDate, 1);   // 1 hour after target

      const appointments = await Appointment.find({
        date: {
          $gte: startWindow,
          $lte: endWindow
        },
        status: { $in: ['pending', 'confirmed'] }
      }).populate('user', 'email firstName lastName name');

      console.log(`Found ${appointments.length} appointments for 24-hour reminders`);

      for (const appointment of appointments) {
        // Check if 24-hour reminder already sent
        const alreadySent = this.reminderLogs.some(log => 
          log.appointmentId === appointment._id.toString() && 
          log.reminderType === '24h' && 
          log.status === 'sent'
        );

        if (alreadySent) {
          continue;
        }

        try {
          if (appointment.user && (appointment.user as any).email) {
            await emailService.sendAppointmentReminder(appointment.user as any, appointment as any);
            
            this.logReminder({
              appointmentId: appointment._id.toString(),
              reminderType: '24h',
              sentAt: new Date(),
              status: 'sent'
            });

            console.log(`24-hour reminder sent to ${(appointment.user as any).email}`);
          }
        } catch (error) {
          console.error(`Failed to send 24-hour reminder for appointment ${appointment._id}:`, error);
          
          this.logReminder({
            appointmentId: appointment._id.toString(),
            reminderType: '24h',
            sentAt: new Date(),
            status: 'failed',
            error: (error as Error).message
          });
        }
      }

      console.log('24-hour reminders completed');
    } catch (error) {
      console.error('Error sending 24-hour reminders:', error);
    }
  }

  // Log reminder attempts
  private logReminder(log: ReminderLog): void {
    this.reminderLogs.push(log);
  }

  // Get reminder logs for admin dashboard
  getReminderLogs(limit: number = 100): ReminderLog[] {
    return this.reminderLogs
      .sort((a, b) => b.sentAt.getTime() - a.sentAt.getTime())
      .slice(0, limit);
  }

  // Get reminder statistics
  getReminderStats(): {
    total48h: number;
    successful48h: number;
    failed48h: number;
    total24h: number;
    successful24h: number;
    failed24h: number;
    last24Hours: number;
  } {
    const last24Hours = addHours(new Date(), -24);
    
    const logs48h = this.reminderLogs.filter(log => log.reminderType === '48h');
    const logs24h = this.reminderLogs.filter(log => log.reminderType === '24h');
    const recentLogs = this.reminderLogs.filter(log => isAfter(log.sentAt, last24Hours));

    return {
      total48h: logs48h.length,
      successful48h: logs48h.filter(log => log.status === 'sent').length,
      failed48h: logs48h.filter(log => log.status === 'failed').length,
      total24h: logs24h.length,
      successful24h: logs24h.filter(log => log.status === 'sent').length,
      failed24h: logs24h.filter(log => log.status === 'failed').length,
      last24Hours: recentLogs.length
    };
  }

  // Clean up old logs (keep only last 30 days)
  private cleanupOldLogs(): void {
    const cutoffDate = addDays(new Date(), -30);
    const initialCount = this.reminderLogs.length;
    
    this.reminderLogs = this.reminderLogs.filter(log => 
      isAfter(log.sentAt, cutoffDate)
    );
    
    const removedCount = initialCount - this.reminderLogs.length;
    if (removedCount > 0) {
      console.log(`Cleaned up ${removedCount} old reminder logs`);
    }
  }

  // Manual trigger for testing
  async sendTestReminder(appointmentId: string, reminderType: '48h' | '24h'): Promise<boolean> {
    try {
      const appointment = await Appointment.findById(appointmentId)
        .populate('user', 'email firstName lastName name');

      if (!appointment || !appointment.user) {
        throw new Error('Appointment or user not found');
      }

      if (reminderType === '48h') {
        await emailService.send48HourReminder(appointment.user as any, appointment as any);
      } else {
        await emailService.sendAppointmentReminder(appointment.user as any, appointment as any);
      }

      this.logReminder({
        appointmentId: appointmentId,
        reminderType,
        sentAt: new Date(),
        status: 'sent'
      });

      return true;
    } catch (error) {
      console.error(`Failed to send test reminder:`, error);
      
      this.logReminder({
        appointmentId: appointmentId,
        reminderType,
        sentAt: new Date(),
        status: 'failed',
        error: (error as Error).message
      });

      return false;
    }
  }
}

export const appointmentReminderService = new AppointmentReminderService();
