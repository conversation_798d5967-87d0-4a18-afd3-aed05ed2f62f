# Time Slot Update Instructions

## Problem
The calendar is still showing the old time schedule (9:00 AM to midnight) instead of the new overnight schedule (12:00 AM to 4:00 AM).

## Solution
I've created a comprehensive update script that will:

1. **Update Global Settings**: Change default business hours from 9 AM-5 PM to 12 AM-4 AM
2. **Update Existing Records**: Convert all existing availability records to use the new time slots
3. **Create Sample Data**: Generate availability for the next 30 days with the new schedule
4. **Fix All Fallbacks**: Update all code that references the old schedule

## How to Run the Update

### Option 1: Using npm script (Recommended)
```bash
npm run update:timeslots
```

### Option 2: Direct execution
```bash
npx ts-node src/scripts/updateTimeSlots.ts
```

### Option 3: Using compiled JavaScript
```bash
node src/.dist/scripts/updateTimeSlots.js
```

## What the Script Does

### 1. Updates Global Availability Settings
- Changes business hours from `09:00-17:00` to `00:00-04:00`
- Updates default time slots to overnight schedule
- Sets working days to all 7 days (for overnight work)
- Maintains 30-minute slot intervals

### 2. Updates Existing Data
- Converts all existing admin availability records
- Replaces old time slots with new overnight slots
- Preserves availability status and reasons

### 3. Creates Sample Availability
- Generates availability for next 30 days
- Uses the new overnight time slots
- Sets all slots as available by default

## New Time Schedule
- **Start Time**: 12:00 AM (00:00)
- **End Time**: 4:00 AM (04:00)
- **Intervals**: 30 minutes
- **Time Slots**:
  - 12:00 AM, 12:30 AM
  - 1:00 AM, 1:30 AM
  - 2:00 AM, 2:30 AM
  - 3:00 AM, 3:30 AM

## Code Changes Made

### Backend Updates
1. **Controllers**: Updated all fallback time slot generation
2. **Services**: Updated default time slot functions
3. **Models**: Updated default business hours
4. **Compiled JS**: Updated all .dist files

### Frontend Updates
1. **Time Formatting**: Created unified time format utilities
2. **Calendar Components**: Updated to use 12-hour AM/PM format
3. **Booking Flow**: Updated time display consistency
4. **Admin Interface**: Updated calendar management

## Verification Steps

After running the update script:

1. **Check Admin Calendar**: 
   - Go to admin panel → Calendar Management
   - Select any date
   - Verify time slots show: 12:00 AM, 12:30 AM, 1:00 AM, etc.

2. **Check User Booking**:
   - Go to booking flow
   - Select a service and date
   - Verify available times show overnight hours

3. **Check Database**:
   - Global settings should have new business hours
   - Existing availability records should have new time slots

## Troubleshooting

### If time slots still show old schedule:
1. Clear browser cache
2. Restart the server: `npm run dev`
3. Check database connection
4. Re-run the update script

### If script fails:
1. Check database connection
2. Ensure MongoDB is running
3. Check environment variables
4. Review error logs

## Important Notes

- **Backup Recommended**: Consider backing up your database before running
- **Server Restart**: Restart your server after running the update
- **Cache Clear**: Clear browser cache to see changes
- **Production**: Test in development first before running in production

## Support

If you encounter any issues:
1. Check the console output for error messages
2. Verify database connectivity
3. Ensure all environment variables are set
4. Contact support with error logs if needed
