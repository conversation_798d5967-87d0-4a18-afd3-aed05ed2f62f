import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Header from '../components/Layout/Header';
import { useToast } from '../contexts/ToastContext';
import { type User, getCurrentUser } from '../utils/api';
import { API_CONFIG } from '../utils/config';
import { serviceAPI, type Service } from '../utils/serviceAPI';

interface Review {
  _id: string;
  service?: {
    _id: string;
    name: string;
  };
  user?: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  appointment?: {
    _id: string;
    date: string;
    time: string;
  };
  rating: number;
  title?: string;
  comment?: string;
  customerName?: string;
  customerEmail?: string;
  status: 'pending' | 'approved' | 'rejected';
  isVerifiedPurchase: boolean;
  helpfulVotes: number;
  createdAt: string;
  updatedAt: string;
  adminResponse?: string;
}

interface ReviewFormData {
  serviceId: string;
  customerName: string;
  customerEmail: string;
  rating: number;
  title: string;
  comment: string;
}

export default function ReviewsPage() {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [allReviews, setAllReviews] = useState<Review[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [reviewsLoading, setReviewsLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [selectedService, setSelectedService] = useState<string>('');
  const [selectedRating, setSelectedRating] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'view' | 'create'>('view');
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { showSuccess, showError } = useToast();

  const [formData, setFormData] = useState<ReviewFormData>({
    serviceId: searchParams.get('service') || '',
    customerName: '',
    customerEmail: '',
    rating: 5,
    title: '',
    comment: ''
  });

  useEffect(() => {
    loadCurrentUser();
    loadServices();
    loadReviews();

    // Check for tab parameter in URL
    const tabParam = searchParams.get('tab');
    if (tabParam === 'create') {
      setActiveTab('create');
    }
  }, []);

  useEffect(() => {
    loadReviews();
  }, [currentPage, selectedService, selectedRating]);

  useEffect(() => {
    // Auto-fill user data if logged in
    if (currentUser) {
      setFormData(prev => ({
        ...prev,
        customerName: `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim(),
        customerEmail: currentUser.email || ''
      }));
    }
  }, [currentUser]);

  const loadCurrentUser = async () => {
    try {
      const user = await getCurrentUser();
      setCurrentUser(user);
    } catch (error) {
      setCurrentUser(null);
    }
  };

  const loadServices = async () => {
    try {
      const response = await serviceAPI.getServices({ isActive: true });
      if (response.success) {
        const allServices: Service[] = [];
        Object.entries(response.data).forEach(([, categoryServices]) => {
          if (Array.isArray(categoryServices)) {
            allServices.push(...categoryServices);
          }
        });
        allServices.sort((a, b) => a.name.localeCompare(b.name));
        setServices(allServices);
      }
    } catch (error) {
      console.error('Error loading services:', error);
    }
  };

  const loadReviews = async () => {
    try {
      setReviewsLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10'
      });
      
      if (selectedService) params.append('service', selectedService);
      if (selectedRating) params.append('rating', selectedRating);

      const response = await fetch(`${API_CONFIG.BASE_URL}/reviews?${params.toString()}`);
      const data = await response.json();

      if (data.success) {
        setAllReviews(data.data.reviews);
        setTotalPages(data.data.pagination.pages);
      } else {
        showError('Failed to load reviews');
      }
    } catch (error) {
      console.error('Error loading reviews:', error);
      showError('Failed to load reviews');
    } finally {
      setReviewsLoading(false);
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRatingChange = (rating: number) => {
    setFormData(prev => ({
      ...prev,
      rating
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.serviceId) {
      showError('Please select a service');
      return false;
    }
    if (!formData.customerName.trim()) {
      showError('Please enter your name');
      return false;
    }
    if (!formData.customerEmail.trim()) {
      showError('Please enter your email address');
      return false;
    }
    if (!formData.comment.trim()) {
      showError('Please write a review comment');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setSubmitting(true);

      const response = await fetch(`${API_CONFIG.BASE_URL}/reviews/public`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          serviceId: formData.serviceId,
          customerName: formData.customerName.trim(),
          customerEmail: formData.customerEmail.trim(),
          rating: formData.rating,
          title: formData.title.trim(),
          comment: formData.comment.trim()
        })
      });

      const data = await response.json();

      if (data.success) {
        showSuccess('Review submitted successfully and is pending approval!');
        setActiveTab('view');
        setFormData({
          serviceId: '',
          customerName: currentUser ? `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim() : '',
          customerEmail: currentUser?.email || '',
          rating: 5,
          title: '',
          comment: ''
        });
        loadReviews(); // Refresh reviews
      } else {
        showError(data.message || 'Failed to submit review');
      }
    } catch (err) {
      console.error('Error submitting review:', err);
      showError('Failed to submit review. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const renderStarRating = (rating: number, interactive: boolean = false) => {
    return (
      <div className="star-rating">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={interactive ? () => handleRatingChange(star) : undefined}
            className={`star ${interactive ? 'interactive' : ''} ${star <= rating ? 'filled' : 'empty'}`}
            disabled={!interactive}
          >
            ★
          </button>
        ))}
      </div>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getReviewerName = (review: Review) => {
    if (review.user) {
      return `${review.user.firstName} ${review.user.lastName}`;
    }
    return review.customerName || 'Anonymous';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header currentUser={currentUser} onLogout={() => {}} />
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="app">
      <Header currentUser={currentUser} onLogout={() => {
        localStorage.removeItem('authToken');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        setCurrentUser(null);
        navigate('/');
      }} />

      <main className="main-content">
        {/* Header Section */}
        <div className="page-header">
          <h1>Client Reviews</h1>
          <p>See what our clients are saying and share your own experience</p>
        </div>

        {/* Tab Navigation */}
        <div className="reviews-tabs">
          <button
            onClick={() => setActiveTab('view')}
            className={`tab-btn ${activeTab === 'view' ? 'active' : ''}`}
          >
            View Reviews
          </button>
          <button
            onClick={() => setActiveTab('create')}
            className={`tab-btn ${activeTab === 'create' ? 'active' : ''}`}
          >
            Write Review
          </button>
        </div>

        {/* View Reviews Tab */}
        {activeTab === 'view' && (
          <>
            {/* Filters */}
            <div className="reviews-filters">
              <h3>Filter Reviews</h3>
              <div className="filter-controls">
                <div className="filter-group">
                  <label>Service</label>
                  <select
                    value={selectedService}
                    onChange={(e) => {
                      setSelectedService(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="filter-select"
                  >
                    <option value="">All Services</option>
                    {services.map((service) => (
                      <option key={service.id} value={service.id}>
                        {service.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="filter-group">
                  <label>Rating</label>
                  <select
                    value={selectedRating}
                    onChange={(e) => {
                      setSelectedRating(e.target.value);
                      setCurrentPage(1);
                    }}
                    className="filter-select"
                  >
                    <option value="">All Ratings</option>
                    <option value="5">5 Stars</option>
                    <option value="4">4 Stars</option>
                    <option value="3">3 Stars</option>
                    <option value="2">2 Stars</option>
                    <option value="1">1 Star</option>
                  </select>
                </div>

                <div className="filter-group">
                  <button
                    onClick={() => {
                      setSelectedService('');
                      setSelectedRating('');
                      setCurrentPage(1);
                    }}
                    className="btn-secondary"
                  >
                    Clear Filters
                  </button>
                </div>
              </div>
            </div>

            {/* Reviews List */}
            {reviewsLoading ? (
              <div className="loading-container">
                <p>Loading reviews...</p>
              </div>
            ) : allReviews.length === 0 ? (
              <div className="empty-state">
                <h3>No reviews found</h3>
                <p>Be the first to share your experience!</p>
                <button
                  onClick={() => setActiveTab('create')}
                  className="btn-primary"
                >
                  Write the First Review
                </button>
              </div>
            ) : (
              <div className="reviews-grid">
                {allReviews.map((review) => (
                  <div key={review._id} className="review-card">
                    <div className="review-header">
                      <div className="review-rating">
                        {renderStarRating(review.rating)}
                        {review.isVerifiedPurchase && (
                          <span className="verified-badge">
                            Verified Purchase
                          </span>
                        )}
                      </div>
                      <div className="review-meta">
                        <p className="reviewer-name">{getReviewerName(review)}</p>
                        <p className="review-date">{formatDate(review.createdAt)}</p>
                      </div>
                    </div>

                    {review.title && (
                      <h4 className="review-title">{review.title}</h4>
                    )}

                    {review.service && (
                      <p className="review-service">
                        Service: <span>{review.service.name}</span>
                      </p>
                    )}

                    {review.comment && (
                      <p className="review-comment">{review.comment}</p>
                    )}

                    {review.adminResponse && (
                      <div className="admin-response">
                        <p className="response-label">Response from Business:</p>
                        <p className="response-text">{review.adminResponse}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="pagination">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="pagination-btn"
                >
                  Previous
                </button>

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                  <button
                    key={page}
                    onClick={() => setCurrentPage(page)}
                    className={`pagination-btn ${currentPage === page ? 'active' : ''}`}
                  >
                    {page}
                  </button>
                ))}

                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="pagination-btn"
                >
                  Next
                </button>
              </div>
            )}
          </>
        )}

        {/* Create Review Tab */}
        {activeTab === 'create' && (
          <div className="review-form-container">
            <div className="review-form-card">
              <h2>Share Your Experience</h2>

              <form onSubmit={handleSubmit} className="review-form">
                {/* Service Selection */}
                <div className="form-group">
                  <label htmlFor="serviceId">Service Received *</label>
                  <select
                    id="serviceId"
                    name="serviceId"
                    value={formData.serviceId}
                    onChange={handleInputChange}
                    required
                    className="form-select"
                  >
                    <option value="">Select a service...</option>
                    {services.map((service) => (
                      <option key={service.id} value={service.id}>
                        {service.name} - ${service.price}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Customer Name */}
                <div className="form-group">
                  <label htmlFor="customerName">Your Name *</label>
                  <input
                    type="text"
                    id="customerName"
                    name="customerName"
                    value={formData.customerName}
                    onChange={handleInputChange}
                    required
                    className="form-input"
                    placeholder="Enter your full name"
                  />
                </div>

                {/* Customer Email */}
                <div className="form-group">
                  <label htmlFor="customerEmail">Your Email *</label>
                  <input
                    type="email"
                    id="customerEmail"
                    name="customerEmail"
                    value={formData.customerEmail}
                    onChange={handleInputChange}
                    required
                    className="form-input"
                    placeholder="Enter your email address"
                  />
                  <p className="form-help">
                    We'll use this to verify your review and contact you if needed
                  </p>
                </div>

                {/* Rating */}
                <div className="form-group">
                  <label>Your Rating *</label>
                  <div className="rating-container">
                    {renderStarRating(formData.rating, true)}
                    <span className="rating-text">
                      {formData.rating} out of 5 stars
                    </span>
                  </div>
                </div>

                {/* Review Title */}
                <div className="form-group">
                  <label htmlFor="title">Review Title (Optional)</label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    className="form-input"
                    placeholder="Summarize your experience in a few words"
                  />
                </div>

                {/* Review Comment */}
                <div className="form-group">
                  <label htmlFor="comment">Your Review *</label>
                  <textarea
                    id="comment"
                    name="comment"
                    value={formData.comment}
                    onChange={handleInputChange}
                    required
                    rows={6}
                    className="form-textarea"
                    placeholder="Share your experience with this service. What did you like? How was the quality? Would you recommend it to others?"
                  />
                  <p className="form-help">
                    Minimum 10 characters. Be honest and helpful to other clients.
                  </p>
                </div>

                {/* Submit Button */}
                <div className="form-actions">
                  <button
                    type="button"
                    onClick={() => setActiveTab('view')}
                    className="btn-secondary"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={submitting}
                    className="btn-primary"
                  >
                    {submitting ? 'Submitting...' : 'Submit Review'}
                  </button>
                </div>
              </form>

              <div className="form-guidelines">
                <p>
                  <strong>Review Guidelines:</strong> Please be honest and constructive.
                  Reviews containing inappropriate language or false information will not be approved.
                </p>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
