import { type Service } from '../../utils/serviceAPI';

interface ServiceListProps {
  services: Service[];
  viewMode: 'grid' | 'table';
  selectedServices: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  onEdit: (service: Service) => void;
  onDelete: (serviceId: string) => void;
  allSelected: boolean;
  someSelected: boolean;
  searchTerm: string;
}

export default function ServiceList({
  services,
  viewMode,
  selectedServices,
  onSelectionChange,
  onEdit,
  onDelete,
  allSelected,
  someSelected,
  searchTerm
}: ServiceListProps) {
  // Handle select all
  const handleSelectAll = () => {
    if (allSelected) {
      onSelectionChange([]);
    } else {
      onSelectionChange(services.map(service => service.id));
    }
  };

  // Handle individual selection
  const handleSelectService = (serviceId: string) => {
    if (selectedServices.includes(serviceId)) {
      onSelectionChange(selectedServices.filter(id => id !== serviceId));
    } else {
      onSelectionChange([...selectedServices, serviceId]);
    }
  };

  // Highlight search terms
  const highlightText = (text: string, searchTerm: string) => {
    if (!searchTerm) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200">{part}</mark>
      ) : (
        part
      )
    );
  };

  // Format duration
  const formatDuration = (hours: number) => {
    if (hours < 1) {
      return `${Math.round(hours * 60)} min`;
    } else if (hours === 1) {
      return '1 hour';
    } else if (hours % 1 === 0) {
      return `${hours} hours`;
    } else {
      const wholeHours = Math.floor(hours);
      const minutes = Math.round((hours - wholeHours) * 60);
      return `${wholeHours}h ${minutes}m`;
    }
  };

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  if (services.length === 0) {
    return (
      <div className="appointment-list-empty">
        <div className="empty-state">
          <div className="empty-icon">🔍</div>
          <h3>No services found</h3>
          <p>Try adjusting your search criteria or filters.</p>
        </div>
      </div>
    );
  }

  if (viewMode === 'grid') {
    return (
      <div className="services-grid">
        {services.map((service) => (
          <div key={service.id} className="service-card">
            {/* Selection checkbox */}
            <div className="absolute top-6 left-6">
              <input
                type="checkbox"
                checked={selectedServices.includes(service.id)}
                onChange={() => handleSelectService(service.id)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </div>

            <div className="service-header" style={{ marginTop: '8px' }}>
              <h3 style={{ fontSize: '18px', fontWeight: '600', marginBottom: '8px' }}>
                {highlightText(service.name, searchTerm)}
              </h3>
              <div className="service-price" style={{ fontSize: '20px', fontWeight: '700', color: '#059669' }}>
                {formatPrice(service.price)}
              </div>
            </div>

            <div className="service-description" style={{
              color: '#6b7280',
              marginBottom: '16px',
              lineHeight: '1.5',
              fontSize: '14px'
            }}>
              {highlightText(service.description, searchTerm)}
            </div>

            <div className="service-details" style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
              marginBottom: '20px'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{
                  backgroundColor: '#f3f4f6',
                  color: '#374151',
                  padding: '4px 8px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  fontWeight: '500'
                }}>
                  {service.category}
                </span>
                <span style={{
                  backgroundColor: '#dbeafe',
                  color: '#1e40af',
                  padding: '4px 8px',
                  borderRadius: '6px',
                  fontSize: '12px',
                  fontWeight: '500'
                }}>
                  {formatDuration(service.duration)}
                </span>
              </div>
              <span className={`status-badge ${service.isActive ? 'status-confirmed' : 'status-cancelled'}`}>
                {service.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>

            <div className="service-actions" style={{ display: 'flex', gap: '8px' }}>
              <button
                onClick={() => onEdit(service)}
                className="btn btn-sm btn-outline"
                style={{ flex: '1' }}
              >
                Edit
              </button>
              <button
                onClick={() => onDelete(service.id)}
                className="btn btn-sm btn-danger"
                style={{ flex: '1' }}
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="appointment-table-container">
      <table className="appointment-table">
        <thead>
          <tr>
            <th className="checkbox-column">
              <input
                type="checkbox"
                checked={allSelected}
                ref={(input) => {
                  if (input) input.indeterminate = someSelected && !allSelected;
                }}
                onChange={handleSelectAll}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
            </th>
            <th className="sortable">Service</th>
            <th className="sortable">Category</th>
            <th className="sortable">Price</th>
            <th className="sortable">Duration</th>
            <th className="sortable">Status</th>
            <th className="sortable">Created</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {services.map((service) => (
            <tr key={service.id} className="appointment-row">
              <td className="checkbox-column">
                <input
                  type="checkbox"
                  checked={selectedServices.includes(service.id)}
                  onChange={() => handleSelectService(service.id)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </td>
              
              <td className="service-info">
                <div className="service-name">
                  {highlightText(service.name, searchTerm)}
                </div>
                <div className="text-sm text-gray-600 line-clamp-2">
                  {highlightText(service.description, searchTerm)}
                </div>
              </td>
              
              <td>
                <span className="service-category">
                  {service.category}
                </span>
              </td>
              
              <td>
                <div className="price">
                  {formatPrice(service.price)}
                </div>
              </td>
              
              <td>
                <span className="text-sm text-gray-600">
                  {formatDuration(service.duration)}
                </span>
              </td>
              
              <td>
                <span className={`status-badge ${service.isActive ? 'status-confirmed' : 'status-cancelled'}`}>
                  {service.isActive ? 'Active' : 'Inactive'}
                </span>
              </td>
              
              <td>
                <div className="text-sm text-gray-600">
                  {service.createdAt ? new Date(service.createdAt).toLocaleDateString() : 'N/A'}
                </div>
              </td>
              
              <td>
                <div className="action-buttons">
                  <button
                    onClick={() => onEdit(service)}
                    className="btn btn-sm btn-outline"
                    title="Edit service"
                    style={{
                      padding: '6px 8px',
                      minWidth: '32px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    onClick={() => onDelete(service.id)}
                    className="btn btn-sm btn-danger"
                    title="Delete service"
                    style={{
                      padding: '6px 8px',
                      minWidth: '32px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
