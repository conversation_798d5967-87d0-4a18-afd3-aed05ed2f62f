import { useState, useEffect } from 'react';
import { reviewAPI, type ServiceReviewsResponse } from '../utils/reviewAPI';

interface Service {
  _id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
  category: string;
  isActive: boolean;
  image?: string;
  images?: string[];
}

interface ServiceCardWithReviewsProps {
  service: Service;
  onSelect: (service: Service) => void;
  isSelected?: boolean;
  showReviews?: boolean;
}

export default function ServiceCardWithReviews({ 
  service, 
  onSelect, 
  isSelected = false,
  showReviews = true 
}: ServiceCardWithReviewsProps) {
  const [reviewsData, setReviewsData] = useState<ServiceReviewsResponse | null>(null);
  const [loadingReviews, setLoadingReviews] = useState(false);
  const [showAllReviews, setShowAllReviews] = useState(false);

  useEffect(() => {
    if (showReviews) {
      loadReviews();
    }
  }, [service._id, showReviews]);

  const loadReviews = async () => {
    try {
      setLoadingReviews(true);
      const data = await reviewAPI.getServiceReviews(service._id, 1, 3); // Load first 3 reviews
      setReviewsData(data);
    } catch (error) {
      console.error('Error loading reviews:', error);
    } finally {
      setLoadingReviews(false);
    }
  };

  const renderStars = (rating: number, size: 'sm' | 'md' = 'sm') => {
    const starSize = size === 'sm' ? 'text-sm' : 'text-lg';
    return (
      <div className="flex items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <span
            key={star}
            className={`${starSize} ${
              star <= rating ? 'text-yellow-400' : 'text-gray-300'
            }`}
          >
            ★
          </span>
        ))}
      </div>
    );
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border-2 transition-all duration-200 hover:shadow-md ${
      isSelected ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-200 hover:border-gray-300'
    }`}>
      {/* Service Image(s) */}
      {(service.image || (service.images && service.images.length > 0)) && (
        <div className="service-card-images">
          {/* Main Image */}
          <div className="main-service-image">
            <img
              src={service.image || (service.images && service.images[0]) || ''}
              alt={service.name}
              className="w-full h-48 object-cover rounded-t-lg"
              onError={(e) => {
                // If main image fails, try first image from array
                if (service.image && service.images && service.images.length > 0) {
                  e.currentTarget.src = service.images[0];
                } else {
                  e.currentTarget.style.display = 'none';
                }
              }}
            />

            {/* Image count badge */}
            {service.images && service.images.length > 1 && (
              <div className="image-count-badge">
                +{service.images.length - 1} more
              </div>
            )}
          </div>

          {/* Additional Images Preview (if more than 1) */}
          {service.images && service.images.length > 1 && (
            <div className="additional-images-preview">
              {service.images.slice(1, 4).map((imageUrl: string, index: number) => (
                <img
                  key={index}
                  src={imageUrl}
                  alt={`${service.name} - Style ${index + 2}`}
                  className="preview-image"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
              ))}
            </div>
          )}
        </div>
      )}

      <div className="p-6">
        {/* Service Header */}
        <div className="flex justify-between items-start mb-3">
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900 mb-1">{service.name}</h3>
            <p className="text-sm text-gray-600 mb-2">{service.description}</p>
          </div>
          <div className="text-right ml-4">
            <div className="text-xl font-bold text-gray-900">{formatPrice(service.price)}</div>
            <div className="text-sm text-gray-500">{service.duration} min</div>
          </div>
        </div>

        {/* Reviews Summary */}
        {showReviews && reviewsData && reviewsData.stats.totalReviews > 0 && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center space-x-2">
                {renderStars(Math.round(reviewsData.stats.averageRating))}
                <span className="text-sm font-medium text-gray-900">
                  {reviewsData.stats.averageRating.toFixed(1)}
                </span>
                <span className="text-sm text-gray-500">
                  ({reviewsData.stats.totalReviews} review{reviewsData.stats.totalReviews !== 1 ? 's' : ''})
                </span>
              </div>
              {reviewsData.stats.totalReviews > 3 && (
                <button
                  onClick={() => setShowAllReviews(!showAllReviews)}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  {showAllReviews ? 'Show Less' : 'View All'}
                </button>
              )}
            </div>

            {/* Recent Reviews */}
            {reviewsData.reviews.length > 0 && (
              <div className="space-y-2">
                {(showAllReviews ? reviewsData.reviews : reviewsData.reviews.slice(0, 2)).map((review) => (
                  <div key={review._id} className="text-sm">
                    <div className="flex items-center space-x-2 mb-1">
                      {renderStars(review.rating, 'sm')}
                      <span className="text-gray-600">
                        {review.customerName || 'Anonymous'}
                      </span>
                      {review.isVerifiedPurchase && (
                        <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs bg-green-100 text-green-800">
                          ✓
                        </span>
                      )}
                    </div>
                    {review.title && (
                      <p className="font-medium text-gray-900 mb-1">{review.title}</p>
                    )}
                    {review.comment && (
                      <p className="text-gray-700 line-clamp-2">{review.comment}</p>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Loading Reviews */}
        {showReviews && loadingReviews && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-3/4"></div>
            </div>
          </div>
        )}

        {/* No Reviews */}
        {showReviews && reviewsData && reviewsData.stats.totalReviews === 0 && (
          <div className="mb-4 p-3 bg-gray-50 rounded-lg text-center">
            <p className="text-sm text-gray-500">No reviews yet</p>
            <p className="text-xs text-gray-400">Be the first to leave a review!</p>
          </div>
        )}

        {/* Service Category */}
        <div className="flex items-center justify-between">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {service.category}
          </span>
          
          <button
            onClick={() => onSelect(service)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              isSelected
                ? 'bg-blue-600 text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {isSelected ? 'Selected' : 'Select Service'}
          </button>
        </div>
      </div>
    </div>
  );
}
