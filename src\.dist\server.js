"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const morgan_1 = __importDefault(require("morgan"));
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = require("./config/database");
const config_1 = require("./config");
const routes_1 = __importDefault(require("./routes"));
const v2_1 = __importDefault(require("./routes/v2"));
const errorHandler_1 = require("./middleware/errorHandler");
const initializeServer_1 = require("./utils/initializeServer");
const scheduledEmailService_1 = require("./services/scheduledEmailService");
const appointmentReminderService_1 = require("./services/appointmentReminderService");
const controllers_1 = require("./controllers");
const auth_1 = require("./middleware/auth");
const validation_1 = require("./middleware/validation");
const validation_2 = require("./utils/validation");
// Load environment variables
dotenv_1.default.config();
// Create Express app
const app = (0, express_1.default)();
// Trust proxy
app.set('trust proxy', 1);
// Security middleware
app.use((0, helmet_1.default)({
    crossOriginResourcePolicy: { policy: "cross-origin" }
}));
// CORS configuration - Allow all origins
app.use((0, cors_1.default)({
    origin: '*', // Allow all origins
    credentials: false, // Set to false when using origin: '*'
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'Origin'
    ],
    optionsSuccessStatus: 200
}));
// Compression middleware
app.use((0, compression_1.default)());
// Logging middleware
if (config_1.config.NODE_ENV === 'development') {
    app.use((0, morgan_1.default)('dev'));
}
else {
    app.use((0, morgan_1.default)('combined'));
}
// Body parsing middleware
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Static files (for uploaded files)
app.use('/uploads', express_1.default.static('uploads'));
// Handle preflight requests explicitly
app.options('*', (0, cors_1.default)());
// API routes
app.use('/api', routes_1.default);
// V2 API routes
app.use('/api/v2', v2_1.default);
// ===== V2 API ROUTES (Individual routes - can be removed after testing) =====
// V2 Auth routes - Note: Registration removed, signup functionality disabled
app.post('/api/v2/auth/login', (0, validation_1.validate)(validation_2.loginValidation), controllers_1.AuthController.login);
app.post('/api/v2/auth/forgot-password', (0, validation_1.validate)(validation_2.forgotPasswordValidation), controllers_1.AuthController.forgotPassword);
app.post('/api/v2/auth/reset-password', controllers_1.AuthController.resetPassword);
app.get('/api/v2/auth/verify', auth_1.authenticate, controllers_1.AuthController.verify);
app.post('/api/v2/auth/logout', auth_1.authenticate, controllers_1.AuthController.logout);
app.get('/api/v2/auth/me', auth_1.authenticate, (req, res) => {
    const user = req.user;
    if (!user) {
        return res.status(401).json({ success: false, message: 'User not authenticated' });
    }
    res.json({
        success: true,
        data: {
            id: user._id,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            phone: user.phone,
            role: user.role,
            createdAt: user.createdAt
        }
    });
});
// V2 Health check endpoint
app.get('/api/v2/health', (_req, res) => {
    res.json({
        success: true,
        message: 'API v2 is running',
        version: '2.0.0',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development',
        endpoints: {
            auth: '/api/v2/auth',
            services: '/api/v2/services',
            appointments: '/api/v2/appointments',
            paymentConfirmations: '/api/v2/payment-confirmations',
            user: '/api/v2/user',
            admin: '/api/v2/admin'
        }
    });
});
// Root endpoint
app.get('/', (_req, res) => {
    res.json({
        success: true,
        message: 'Welcome to MicroLocs Backend API',
        version: '1.0.0',
        documentation: '/api/health',
        endpoints: {
            auth: '/api/auth',
            appointments: '/api/appointments',
            services: '/api/services',
            products: '/api/products',
            cart: '/api/cart',
            orders: '/api/orders',
            users: '/api/users',
            notifications: '/api/notifications',
            admin: '/api/admin'
        }
    });
});
// 404 handler
app.use(errorHandler_1.notFound);
// Error handling middleware
app.use(errorHandler_1.errorHandler);
// Start server
const startServer = async () => {
    try {
        // Connect to database
        await (0, database_1.connectDatabase)();
        // Initialize server (create default admin, etc.)
        await (0, initializeServer_1.initializeServer)();
        // Start listening
        const PORT = Number(config_1.config.PORT) || 3000;
        const HOST = config_1.config.HOST || '0.0.0.0';
        app.listen(PORT, HOST, () => {
            console.log(`
🚀 Server is running on ${HOST}:${PORT}
📊 Environment: ${config_1.config.NODE_ENV}
🔗 Local API URL: http://localhost:${PORT}
🌐 Public API URL: http://${HOST}:${PORT}
📚 Health Check: http://${HOST}:${PORT}/api/health
🔧 Admin Panel: http://${HOST}:${PORT}/api/admin
      `);
            // Initialize scheduled email service
            try {
                scheduledEmailService_1.scheduledEmailService.init();
                console.log('📧 Email scheduling service initialized');
            }
            catch (error) {
                console.error('Failed to initialize email scheduling service:', error);
            }
            // Initialize appointment reminder service
            try {
                appointmentReminderService_1.appointmentReminderService.init();
                console.log('⏰ Appointment reminder service initialized');
            }
            catch (error) {
                console.error('Failed to initialize appointment reminder service:', error);
            }
        });
    }
    catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
};
// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
    console.error('Unhandled Promise Rejection:', err);
    process.exit(1);
});
// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err);
    process.exit(1);
});
// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    process.exit(0);
});
process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    process.exit(0);
});
// Start the server
startServer();
exports.default = app;
