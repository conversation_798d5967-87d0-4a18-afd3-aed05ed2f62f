// Service API utilities and interfaces

import { API_CONFIG } from './config';

const API_BASE_URL = API_CONFIG.BASE_URL;

// Service interfaces
export interface Service {
  id: string;
  name: string;
  description: string;
  category: string;
  duration: number;
  price: number;
  isActive?: boolean;
  image?: string;
  images?: string[];
  createdAt?: string;
  updatedAt?: string;
  displayCategory?: string; // For custom category display names
}

export interface ServiceListResponse {
  success: boolean;
  message: string;
  data: {
    consultation: Service[];
    installation: Service[];
    locsstyling: Service[];
    otherservices: Service[];
    retie: Service[];
    'retightening4-5weeks': Service[];
    'retightening6-7week': Service[];
    'retightening8+week': Service[];
    [key: string]: Service[]; // Add index signature
  };
}

export interface AdminServiceListResponse {
  success: boolean;
  data: {
    services: Service[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface ServiceFilters {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  isActive?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface CreateServiceData {
  name: string;
  description: string;
  category: string;
  duration: number;
  price: number;
  isActive?: boolean;
  image?: string;
  images?: string[];
}

export interface UpdateServiceData {
  name?: string;
  description?: string;
  category?: string;
  duration?: number;
  price?: number;
  isActive?: boolean;
  image?: string;
  images?: string[];
}

// Helper function for API requests
async function apiRequest(endpoint: string, options: RequestInit = {}): Promise<any> {
  const token = localStorage.getItem('authToken') || localStorage.getItem('token');
  
  const config: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
    ...options,
  };

  const response = await fetch(`${API_BASE_URL}${endpoint}`, config);
  
  if (!response.ok) {
    const error = await response.json().catch(() => ({ message: 'Network error' }));
    throw new Error(error.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// Service API functions
export const serviceAPI = {
  // Get all services with filtering and pagination (public endpoint)
  getServices: async (filters: ServiceFilters = {}): Promise<ServiceListResponse> => {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, value.toString());
      }
    });

    return await apiRequest(`/services?${params.toString()}`);
  },

  // Get all services for admin with filtering and pagination
  getAdminServices: async (filters: ServiceFilters = {}): Promise<AdminServiceListResponse> => {
    const params = new URLSearchParams();

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        params.set(key, value.toString());
      }
    });

    return await apiRequest(`/admin/services?${params.toString()}`);
  },

  // Get single service details
  getService: async (id: string): Promise<{ success: boolean; data: Service }> => {
    return await apiRequest(`/services/${id}`);
  },

  // Create new service
  createService: async (serviceData: CreateServiceData): Promise<{ success: boolean; data: Service }> => {
    return await apiRequest('/admin/services', {
      method: 'POST',
      body: JSON.stringify(serviceData),
    });
  },

  // Update service
  updateService: async (id: string, updateData: UpdateServiceData): Promise<{ success: boolean; data: Service }> => {
    return await apiRequest(`/admin/services/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updateData),
    });
  },

  // Delete service
  deleteService: async (id: string): Promise<{ success: boolean; data: any }> => {
    return await apiRequest(`/admin/services/${id}`, {
      method: 'DELETE',
    });
  },

  // Get service categories
  getCategories: async (): Promise<{ success: boolean; data: string[] }> => {
    return await apiRequest('/services/categories');
  },

  // Create category (through service creation)
  createCategory: async (_categoryName: string): Promise<{ success: boolean; data: any }> => {
    // Categories are created implicitly when services are created
    // This is a placeholder for future category management API
    return { success: true, data: { message: 'Category will be created when first service is added' } };
  },

  // Update category (rename all services in category)
  updateCategory: async (oldName: string, newName: string): Promise<{ success: boolean; data: any }> => {
    // This would require a backend endpoint to update all services with the old category name
    return await apiRequest('/admin/categories/rename', {
      method: 'PUT',
      body: JSON.stringify({ oldName, newName }),
    });
  },

  // Delete category (handle services in category)
  deleteCategory: async (categoryName: string): Promise<{ success: boolean; data: any }> => {
    // This would require a backend endpoint to handle category deletion
    return await apiRequest(`/admin/categories/${encodeURIComponent(categoryName)}`, {
      method: 'DELETE',
    });
  },
};
