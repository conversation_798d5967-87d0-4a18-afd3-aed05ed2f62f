import { useState, useEffect } from 'react'
import { FiShoppingCart, FiUser, FiCreditCard, FiUpload, FiArrowLeft, FiEye, FiEyeOff, FiRefreshCw } from 'react-icons/fi'
import { useCart } from '../contexts/CartContext'
import { useBranding } from '../contexts/BrandingContext'
import { useToast } from '../contexts/ToastContext'

const Checkout = ({ onNavigate, user, userProfile }) => {
  const { cartItems, getCartTotal, clearCart, isLoading: cartLoading } = useCart()
  const { branding } = useBranding()
  const { showSuccess, showError } = useToast()

  const [step, setStep] = useState(1) // 1: Details, 2: Payment, 3: Confirmation
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  
  // Customer details
  const [customerDetails, setCustomerDetails] = useState({
    firstName: userProfile?.firstName || user?.firstName || '',
    lastName: userProfile?.lastName || user?.lastName || '',
    email: userProfile?.email || user?.email || '',
    phone: userProfile?.phone || user?.phone || '',
    address: userProfile?.address || user?.address || '',
    city: userProfile?.city || user?.city || '',
    state: userProfile?.state || user?.state || '',
    zipCode: userProfile?.zipCode || user?.zipCode || '',
    password: '', // For guest account creation
    createAccount: !user // Auto-check if not logged in
  })

  // Payment details
  const [paymentDetails, setPaymentDetails] = useState({
    method: 'bank_transfer', // bank_transfer, cash, card
    proofImage: null,
    proofImageFile: null,
    notes: ''
  })

  useEffect(() => {
    // Only redirect if cart is definitely empty and not loading
    if (!cartLoading && cartItems.length === 0) {
      onNavigate('shop')
    }
  }, [cartItems, cartLoading, onNavigate])

  // Update form when userProfile changes
  useEffect(() => {
    if (userProfile && user) {
      setCustomerDetails(prev => ({
        ...prev,
        firstName: userProfile.firstName || prev.firstName,
        lastName: userProfile.lastName || prev.lastName,
        email: userProfile.email || prev.email,
        phone: userProfile.phone || prev.phone,
        address: userProfile.address || prev.address,
        city: userProfile.city || prev.city,
        state: userProfile.state || prev.state,
        zipCode: userProfile.zipCode || prev.zipCode,
      }))
    }
  }, [userProfile, user])

  const handleCustomerDetailsChange = (field, value) => {
    setCustomerDetails(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handlePaymentDetailsChange = (field, value) => {
    setPaymentDetails(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const clearCustomerDetails = () => {
    setCustomerDetails({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      state: '',
      zipCode: '',
      password: '',
      createAccount: !user
    })
  }

  const populateUserData = () => {
    if (userProfile && user) {
      setCustomerDetails(prev => ({
        ...prev,
        firstName: userProfile.firstName || '',
        lastName: userProfile.lastName || '',
        email: userProfile.email || '',
        phone: userProfile.phone || '',
        address: userProfile.address || '',
        city: userProfile.city || '',
        state: userProfile.state || '',
        zipCode: userProfile.zipCode || '',
      }))
    }
  }

  const handleImageUpload = (e) => {
    const file = e.target.files[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        showError('Please select a valid image file')
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        showError('Image size must be less than 5MB')
        return
      }

      // Convert to base64 for preview
      const reader = new FileReader()
      reader.onload = (e) => {
        setPaymentDetails(prev => ({
          ...prev,
          proofImage: e.target.result,
          proofImageFile: file
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  const validateStep1 = () => {
    const required = ['firstName', 'lastName', 'phone', 'address', 'city', 'state']
    const isValid = required.every(field => customerDetails[field].trim() !== '')

    // At least email or phone must be provided
    if (!customerDetails.email.trim() && !customerDetails.phone.trim()) {
      showError('Please provide either email or phone number')
      return false
    }

    // If creating account, password is required
    if (customerDetails.createAccount && !user) {
      if (!customerDetails.password || customerDetails.password.length < 6) {
        showError('Password must be at least 6 characters long')
        return false
      }
    }

    // Validate email format if provided
    if (customerDetails.email.trim()) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(customerDetails.email)) {
        showError('Please enter a valid email address')
        return false
      }
    }

    return isValid
  }

  const validateStep2 = () => {
    if (paymentDetails.method === 'bank_transfer' && !paymentDetails.proofImage) {
      showError('Please upload payment proof for bank transfer')
      return false
    }
    return true
  }

  const handleNextStep = () => {
    if (step === 1 && !validateStep1()) {
      return
    }
    if (step === 2 && !validateStep2()) {
      return
    }
    setStep(step + 1)
  }

  const handleSubmitOrder = async () => {
    try {
      setIsSubmitting(true)

      // Create order data
      const orderData = {
        items: cartItems.map(item => ({
          productId: item.id || item._id,
          quantity: item.quantity,
          price: item.price,
          name: item.name
        })),
        total: getCartTotal(),
        customerDetails: {
          firstName: customerDetails.firstName,
          lastName: customerDetails.lastName,
          email: customerDetails.email,
          phone: customerDetails.phone,
          address: customerDetails.address,
          city: customerDetails.city,
          state: customerDetails.state,
          zipCode: customerDetails.zipCode
        },
        paymentDetails: {
          method: paymentDetails.method,
          proofImage: paymentDetails.proofImage,
          notes: paymentDetails.notes
        },
        createAccount: customerDetails.createAccount && !user,
        password: customerDetails.createAccount && !user ? customerDetails.password : undefined
      }

      // Here you would call your API to create the order
      console.log('Order data:', orderData)

      // Simulate API call for now
      await new Promise(resolve => setTimeout(resolve, 2000))

      // If account creation was requested, show additional success message
      if (customerDetails.createAccount && !user) {
        showSuccess('Order placed successfully! Your account has been created and you will receive a confirmation email shortly.')
      } else {
        showSuccess('Order placed successfully! You will receive a confirmation email shortly.')
      }

      clearCart()
      setStep(3)

    } catch (error) {
      console.error('Error placing order:', error)
      showError('Failed to place order. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {[1, 2, 3].map((stepNumber) => (
        <div key={stepNumber} className="flex items-center">
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step >= stepNumber
                ? 'text-white'
                : 'bg-gray-200 text-gray-600'
            }`}
            style={{
              backgroundColor: step >= stepNumber ? branding.colors.secondary : undefined
            }}
          >
            {stepNumber}
          </div>
          {stepNumber < 3 && (
            <div
              className={`w-16 h-1 mx-2 ${
                step > stepNumber ? 'bg-green-500' : 'bg-gray-200'
              }`}
            />
          )}
        </div>
      ))}
    </div>
  )

  const renderCustomerDetails = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">
          {user ? 'Shipping Details' : 'Customer & Shipping Details'}
        </h2>
        {user && userProfile && (
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={populateUserData}
              className="flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
            >
              <FiUser className="w-4 h-4 mr-1" />
              Use My Info
            </button>
            <button
              type="button"
              onClick={clearCustomerDetails}
              className="flex items-center px-3 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors duration-200"
            >
              <FiRefreshCw className="w-4 h-4 mr-1" />
              Clear
            </button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            First Name *
          </label>
          <input
            type="text"
            value={customerDetails.firstName}
            onChange={(e) => handleCustomerDetailsChange('firstName', e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Last Name *
          </label>
          <input
            type="text"
            value={customerDetails.lastName}
            onChange={(e) => handleCustomerDetailsChange('lastName', e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email *
          </label>
          <input
            type="email"
            value={customerDetails.email}
            onChange={(e) => handleCustomerDetailsChange('email', e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Phone *
          </label>
          <input
            type="tel"
            value={customerDetails.phone}
            onChange={(e) => handleCustomerDetailsChange('phone', e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Address *
          </label>
          <input
            type="text"
            value={customerDetails.address}
            onChange={(e) => handleCustomerDetailsChange('address', e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Street address, apartment, suite, etc."
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            City *
          </label>
          <input
            type="text"
            value={customerDetails.city}
            onChange={(e) => handleCustomerDetailsChange('city', e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            State *
          </label>
          <input
            type="text"
            value={customerDetails.state}
            onChange={(e) => handleCustomerDetailsChange('state', e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            ZIP Code
          </label>
          <input
            type="text"
            value={customerDetails.zipCode}
            onChange={(e) => handleCustomerDetailsChange('zipCode', e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Account Creation Section for Guests */}
      {!user && (
        <div className="mt-8 p-6 bg-gray-50 rounded-lg">
          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="createAccount"
              checked={customerDetails.createAccount}
              onChange={(e) => handleCustomerDetailsChange('createAccount', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
            <label htmlFor="createAccount" className="ml-2 text-sm font-medium text-gray-700">
              Create an account for faster checkout next time
            </label>
          </div>

          {customerDetails.createAccount && (
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password * (minimum 6 characters)
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  value={customerDetails.password}
                  onChange={(e) => handleCustomerDetailsChange('password', e.target.value)}
                  className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  {showPassword ? (
                    <FiEyeOff className="w-5 h-5 text-gray-400" />
                  ) : (
                    <FiEye className="w-5 h-5 text-gray-400" />
                  )}
                </button>
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Your account will be created automatically when you complete your order.
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )

  const renderPaymentDetails = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">Payment Details</h2>

      {/* Payment Method Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-4">
          Payment Method *
        </label>
        <div className="space-y-3">
          <div className="flex items-center">
            <input
              type="radio"
              id="bank_transfer"
              name="paymentMethod"
              value="bank_transfer"
              checked={paymentDetails.method === 'bank_transfer'}
              onChange={(e) => handlePaymentDetailsChange('method', e.target.value)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
            />
            <label htmlFor="bank_transfer" className="ml-2 text-sm font-medium text-gray-700">
              Bank Transfer (Upload proof of payment)
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="radio"
              id="cash"
              name="paymentMethod"
              value="cash"
              checked={paymentDetails.method === 'cash'}
              onChange={(e) => handlePaymentDetailsChange('method', e.target.value)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500"
            />
            <label htmlFor="cash" className="ml-2 text-sm font-medium text-gray-700">
              Cash on Delivery
            </label>
          </div>
        </div>
      </div>

      {/* Bank Transfer Details */}
      {paymentDetails.method === 'bank_transfer' && (
        <div className="p-4 bg-blue-50 rounded-lg">
          <h3 className="font-medium text-blue-900 mb-2">Bank Transfer Details</h3>
          <div className="text-sm text-blue-800 space-y-1">
            <p><strong>Bank Name:</strong> {branding?.bankDetails?.bankName || 'Your Bank Name'}</p>
            <p><strong>Account Name:</strong> {branding?.bankDetails?.accountName || 'Your Business Name'}</p>
            <p><strong>Account Number:</strong> {branding?.bankDetails?.accountNumber || '**********'}</p>
            <p><strong>Routing Number:</strong> {branding?.bankDetails?.routingNumber || '*********'}</p>
          </div>
          <p className="text-xs text-blue-600 mt-2">
            Please transfer the exact amount and upload proof of payment below.
          </p>
        </div>
      )}

      {/* Payment Proof Upload */}
      {paymentDetails.method === 'bank_transfer' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Upload Payment Proof *
          </label>
          <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div className="space-y-1 text-center">
              {paymentDetails.proofImage ? (
                <div className="space-y-2">
                  <img
                    src={paymentDetails.proofImage}
                    alt="Payment proof"
                    className="mx-auto h-32 w-auto rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() => setPaymentDetails(prev => ({ ...prev, proofImage: null, proofImageFile: null }))}
                    className="text-sm text-red-600 hover:text-red-800"
                  >
                    Remove image
                  </button>
                </div>
              ) : (
                <>
                  <FiUpload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="flex text-sm text-gray-600">
                    <label
                      htmlFor="payment-proof"
                      className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500"
                    >
                      <span>Upload a file</span>
                      <input
                        id="payment-proof"
                        name="payment-proof"
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="sr-only"
                      />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                  </div>
                  <p className="text-xs text-gray-500">PNG, JPG, GIF up to 5MB</p>
                </>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Additional Notes */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Additional Notes (Optional)
        </label>
        <textarea
          value={paymentDetails.notes}
          onChange={(e) => handlePaymentDetailsChange('notes', e.target.value)}
          rows={3}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          placeholder="Any special instructions or notes for your order..."
        />
      </div>
    </div>
  )

  if (cartLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading checkout...</p>
          </div>
        </div>
      </div>
    )
  }

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <FiShoppingCart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Your cart is empty</h2>
            <p className="text-gray-600 mb-6">Add some products to proceed with checkout</p>
            <button
              onClick={() => onNavigate('shop')}
              className="px-6 py-3 text-white rounded-lg transition-colors duration-200"
              style={{ backgroundColor: branding.colors.secondary }}
            >
              Continue Shopping
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center mb-8">
          <button
            onClick={() => onNavigate('shop')}
            className="flex items-center text-gray-600 hover:text-gray-800 transition-colors duration-200"
          >
            <FiArrowLeft className="w-5 h-5 mr-2" />
            Back to Shop
          </button>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">Checkout</h1>
          
          {renderStepIndicator()}
          
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {step === 1 && renderCustomerDetails()}
              {step === 2 && renderPaymentDetails()}
              {step === 3 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Order Confirmed!</h2>
                  <p className="text-gray-600 mb-6">Thank you for your order. You will receive a confirmation email shortly.</p>
                  <button
                    onClick={() => onNavigate('shop')}
                    className="px-6 py-3 text-white rounded-lg transition-colors duration-200"
                    style={{ backgroundColor: branding.colors.secondary }}
                  >
                    Continue Shopping
                  </button>
                </div>
              )}
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-gray-50 rounded-lg p-6 sticky top-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                
                <div className="space-y-3 mb-4">
                  {cartItems.map((item) => (
                    <div key={item.id} className="flex justify-between">
                      <span className="text-gray-600">{item.name} x {item.quantity}</span>
                      <span className="font-medium">${(item.price * item.quantity).toFixed(2)}</span>
                    </div>
                  ))}
                </div>
                
                <div className="border-t pt-4">
                  <div className="flex justify-between text-lg font-bold">
                    <span>Total:</span>
                    <span style={{ color: branding.colors.secondary }}>${getCartTotal().toFixed(2)}</span>
                  </div>
                </div>
                
                {step < 3 && (
                  <div className="mt-6">
                    {step < 2 ? (
                      <button
                        onClick={handleNextStep}
                        className="w-full py-3 text-white font-medium rounded-lg transition-colors duration-200"
                        style={{ backgroundColor: branding.colors.secondary }}
                      >
                        Continue to Payment
                      </button>
                    ) : (
                      <button
                        onClick={handleSubmitOrder}
                        disabled={isSubmitting}
                        className="w-full py-3 text-white font-medium rounded-lg transition-colors duration-200 disabled:opacity-50"
                        style={{ backgroundColor: branding.colors.secondary }}
                      >
                        {isSubmitting ? 'Placing Order...' : 'Place Order'}
                      </button>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Checkout
