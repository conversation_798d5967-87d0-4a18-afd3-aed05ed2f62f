import { useState, useEffect } from 'react';
import { API_CONFIG } from '../../utils/config';

export default function PoliciesSection() {
  const [brandingData, setBrandingData] = useState<any>(null);

  useEffect(() => {
    const loadBrandingData = async () => {
      try {
        const response = await fetch(`${API_CONFIG.BASE_URL}/branding`);
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data) {
            setBrandingData(data.data);
          }
        }
      } catch (error) {
        console.error('Error loading branding data:', error);
      }
    };

    loadBrandingData();
  }, []);

  const policies = brandingData?.home?.policies;

  return (
    <div className="policies-section">
      <div className="policies-card">
        <h2 className="policies-title">
          {policies?.title || 'Policies & Terms of Service'}
        </h2>

        <div className="policies-content">
          <ul className="policies-list">
            {policies?.sections?.map((section: any, index: number) => (
              <li key={section._id || index}>
                <div className="policy-icon">{section.icon}</div>
                <div className="policy-content">
                  <strong>{section.title}</strong><br />
                  <div dangerouslySetInnerHTML={{ __html: section.content }} />
                </div>
              </li>
            )) || (
              <>
                <li>
                  <strong>TO BOOK:</strong> Full payment is required to secure any service. Payment must be made at the time of booking.
                </li>
                <li>
                  <strong>RESCHEDULING:</strong> Same-day reschedule - loss of payment
                </li>
                <li>
                  <strong>CANCELLATIONS:</strong> All services must be cancelled at least 24 hours before your appointment.
                </li>
                <li>
                  <strong>NO REFUNDS:</strong> All services are final once completed.
                </li>
              </>
            )}

            {/* Payment Options */}
            {policies?.paymentOptions && (
              <li>
                <strong>PAYMENT OPTIONS:</strong>
                <div className="payment-methods-single">
                  <div className="payment-method-combined">
                    {policies.paymentOptions.cashApp && (
                      <div className="payment-option">
                        <strong>Cash App:</strong><br />
                        {policies.paymentOptions.cashApp.number}
                      </div>
                    )}
                    {policies.paymentOptions.zelle && (
                      <div className="payment-option">
                        <strong>Zelle:</strong><br />
                        {policies.paymentOptions.zelle.name && (
                          <span>{policies.paymentOptions.zelle.name}<br /></span>
                        )}
                        {policies.paymentOptions.zelle.number}
                      </div>
                    )}
                  </div>
                </div>
              </li>
            )}
          </ul>
        </div>

        <div className="policies-footer">
          <p>
            {policies?.footerText ||
             `Thank you for choosing ${brandingData?.global?.siteName || 'us'}. We appreciate your business and look forward to serving you with love and excellence!`
            }
          </p>
        </div>
      </div>
    </div>
  )
}
