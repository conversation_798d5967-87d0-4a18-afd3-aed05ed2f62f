import { useState, useEffect } from 'react';
import Calendar from 'react-calendar';
import { format, addDays, startOfWeek, endOfWeek, eachDayOfInterval } from 'date-fns';
// import { availability } from '../../utils/api'; // Removed - using direct API calls
import type { TimeSlot } from '../../utils/api';
import LoadingSpinner from '../LoadingSpinner';
import { API_CONFIG } from '../../utils/config';
import { formatTo12Hour } from '../../utils/timeFormat';

import 'react-calendar/dist/Calendar.css';
import './AdminCalendar.css';
0
interface GlobalSettings {
  businessHours: { start: string; end: string };
  workingDays: number[];
  slotDuration: number;
  isGloballyUnavailable: boolean;
  globalUnavailabilityReason?: string;
  defaultTimeSlots?: TimeSlot[];
}

export default function CalendarManagement() {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedDates, setSelectedDates] = useState<Date[]>([]);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  const [isFullDayUnavailable, setIsFullDayUnavailable] = useState(false);
  const [reason, setReason] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [bulkMode, setBulkMode] = useState(false);
  const [globalSettings, setGlobalSettings] = useState<GlobalSettings | null>(null);
  const [showGlobalSettings, setShowGlobalSettings] = useState(false);
  const [hasExistingAvailability, setHasExistingAvailability] = useState(false);

  // Fetch global settings
  const fetchGlobalSettings = async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/admin/availability/settings`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.error('Global settings API error:', response.status, response.statusText);
        return;
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.error('Global settings API returned non-JSON response');
        return;
      }

      const data = await response.json();
      if (data.success) {
        setGlobalSettings(data.data);
      } else {
        console.error('Global settings API error:', data.message);
      }
    } catch (err) {
      console.error('Error fetching global settings:', err);
    }
  };

  // Fetch availability data for a specific date (admin endpoint)
  const fetchAvailabilityForDate = async (date: Date) => {
    try {
      const dateString = format(date, 'yyyy-MM-dd');
      // Public endpoint - no authentication required
      const response = await fetch(`${API_CONFIG.BASE_URL}/admin/availability/time-slots/${dateString}`);

      if (!response.ok) {
        console.error('Availability API error:', response.status, response.statusText);
        return;
      }

      const data = await response.json();
      if (data.success) {
        setTimeSlots(data.data.timeSlots || []);
        setHasExistingAvailability(data.data.timeSlots && data.data.timeSlots.length > 0);
        setReason(data.data.reason || '');
        setIsFullDayUnavailable(data.data.isFullDayUnavailable || (data.data.timeSlots && data.data.timeSlots.length === 0 && data.data.reason));
      } else {
        // No specific availability set, generate default time slots from global settings
        setHasExistingAvailability(false);
        setReason('');
        setIsFullDayUnavailable(false);

        // Always generate default time slots to show what would be available
        const defaultSlots = generateDefaultTimeSlots();
        setTimeSlots(defaultSlots);
      }
    } catch (err) {
      console.error('Error fetching availability for date:', err);
    }
  };

  // Fetch availability for selected date
  const fetchAvailability = async (date: Date) => {
    try {
      setLoading(true);
      await fetchAvailabilityForDate(date);
    } catch (err) {
      setError('Error fetching availability');
      console.error('Error fetching availability:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGlobalSettings();
    fetchAvailability(selectedDate);
  }, [selectedDate]);

  // Delete availability for a specific date (reset to default)
  const deleteAvailability = async (date: Date) => {
    try {
      setLoading(true);
      setError(null);

      const dateString = format(date, 'yyyy-MM-dd');
      const response = await fetch(`${API_CONFIG.BASE_URL}/admin/availability/${dateString}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        setSuccess('Availability reset to default successfully');
        await fetchAvailabilityForDate(date);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Failed to delete availability');
      }
    } catch (err) {
      console.error('Error deleting availability:', err);
      setError('Failed to delete availability');
    } finally {
      setLoading(false);
    }
  };

  // Generate default time slots
  const generateDefaultTimeSlots = (): TimeSlot[] => {
    if (globalSettings && globalSettings.defaultTimeSlots) {
      return globalSettings.defaultTimeSlots.map((slot: any) => ({
        time: slot.time,
        isAvailable: true
      }));
    } else {
      // Fallback: generate basic time slots (12 AM to 4 AM, 30-minute intervals)
      const slots: TimeSlot[] = [];
      for (let hour = 0; hour < 4; hour++) {
        for (let minute = 0; minute < 60; minute += 30) {
          slots.push({
            time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            isAvailable: true
          });
        }
      }
      return slots;
    }
  };

  // Enable a day by creating default time slots
  const enableDay = async (date: Date) => {
    try {
      setLoading(true);
      setError(null);

      const defaultSlots = generateDefaultTimeSlots();

      const response = await fetch(`${API_CONFIG.BASE_URL}/admin/availability`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          date: format(date, 'yyyy-MM-dd'),
          timeSlots: defaultSlots,
          isFullDayUnavailable: false,
          reason: ''
        })
      });

      const data = await response.json();
      if (data.success) {
        setSuccess('Day enabled successfully with default time slots');
        // Update local state immediately
        setTimeSlots(defaultSlots);
        setIsFullDayUnavailable(false);
        setReason('');
        setHasExistingAvailability(true);
        // Also fetch fresh data to ensure consistency
        await fetchAvailabilityForDate(date);
      } else {
        setError(data.message || 'Failed to enable day');
      }
    } catch (err) {
      console.error('Error enabling day:', err);
      setError('Failed to enable day');
    } finally {
      setLoading(false);
    }
  };

  // Make all time slots available for current date
  const makeAllTimeSlotsAvailable = () => {
    if (timeSlots.length > 0) {
      const updatedSlots = timeSlots.map(slot => ({
        ...slot,
        isAvailable: true
      }));
      setTimeSlots(updatedSlots);
      setIsFullDayUnavailable(false);
    } else {
      // If no time slots exist, generate default ones
      const defaultSlots = generateDefaultTimeSlots();
      setTimeSlots(defaultSlots);
      setIsFullDayUnavailable(false);
      setReason('');
    }
  };

  // Enable all future dates (bulk operation)
  const enableAllFutureDates = async () => {
    try {
      setLoading(true);
      setError(null);

      // Generate dates for the next 90 days
      const futureDates: string[] = [];
      const today = new Date();
      for (let i = 0; i < 90; i++) {
        const futureDate = addDays(today, i);
        futureDates.push(format(futureDate, 'yyyy-MM-dd'));
      }

      const defaultSlots = generateDefaultTimeSlots();

      const response = await fetch(`${API_CONFIG.BASE_URL}/admin/availability/bulk`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          dates: futureDates,
          timeSlots: defaultSlots,
          isFullDayUnavailable: false,
          reason: ''
        })
      });

      const data = await response.json();
      if (data.success) {
        setSuccess(`Successfully enabled ${data.data.totalProcessed} future dates with default availability`);
        // Refresh current date data
        await fetchAvailabilityForDate(selectedDate);
      } else {
        setError(data.message || 'Failed to enable future dates');
      }
    } catch (err) {
      console.error('Error enabling future dates:', err);
      setError('Failed to enable future dates');
    } finally {
      setLoading(false);
    }
  };

  // Handle time slot toggle
  const handleTimeSlotToggle = (index: number) => {
    const newTimeSlots = [...timeSlots];
    newTimeSlots[index] = {
      ...newTimeSlots[index],
      isAvailable: !newTimeSlots[index].isAvailable
    };
    setTimeSlots(newTimeSlots);
  };

  // Handle saving availability
  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      if (bulkMode && selectedDates.length > 0) {
        // Bulk save
        const dates = selectedDates.map(date => format(date, 'yyyy-MM-dd'));
        const response = await fetch(`${API_CONFIG.BASE_URL}/admin/availability/bulk`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            dates,
            timeSlots,
            isFullDayUnavailable,
            reason
          })
        });

        const data = await response.json();
        if (data.success) {
          setSuccess(`Successfully updated ${data.data.totalProcessed} dates`);
          setSelectedDates([]);
          setBulkMode(false);
        } else {
          setError(data.message || 'Failed to save bulk availability');
        }
      } else {
        // Single date save
        const response = await fetch(`${API_CONFIG.BASE_URL}/admin/availability`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            date: format(selectedDate, 'yyyy-MM-dd'),
            timeSlots,
            isFullDayUnavailable,
            reason
          })
        });

        const data = await response.json();
        if (data.success) {
          setSuccess('Availability saved successfully');
          await fetchAvailabilityForDate(selectedDate);
        } else {
          setError(data.message || 'Failed to save availability');
        }
      }
    } catch (err) {
      setError('Error saving availability');
      console.error('Error saving availability:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle bulk operations (currently unused)
  /*
  const handleSelectAllUnavailable = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_CONFIG.BASE_URL}/admin/availability/settings`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isGloballyUnavailable: true,
          globalUnavailabilityReason: 'Globally unavailable'
        })
      });

      const data = await response.json();
      if (data.success) {
        setSuccess('All dates marked as unavailable');
        await fetchGlobalSettings();
      } else {
        setError(data.message || 'Failed to update global settings');
      }
    } catch (err) {
      setError('Error updating global availability');
      console.error('Error updating global availability:', err);
    } finally {
      setLoading(false);
    }
  };
  */

  const handleToggleGlobalAvailability = async () => {
    if (!globalSettings) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_CONFIG.BASE_URL}/admin/availability/settings`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isGloballyUnavailable: !globalSettings.isGloballyUnavailable,
          globalUnavailabilityReason: globalSettings.isGloballyUnavailable ? '' : 'Globally unavailable'
        })
      });

      const data = await response.json();
      if (data.success) {
        setSuccess(`Global availability ${globalSettings.isGloballyUnavailable ? 'enabled' : 'disabled'}`);
        await fetchGlobalSettings();
      } else {
        setError(data.message || 'Failed to update global settings');
      }
    } catch (err) {
      setError('Error updating global availability');
      console.error('Error updating global availability:', err);
    } finally {
      setLoading(false);
    }
  };

  // Handle date selection for bulk mode
  const handleDateSelect = (date: Date) => {
    if (bulkMode) {
      const dateString = date.toDateString();
      const isSelected = selectedDates.some(d => d.toDateString() === dateString);

      if (isSelected) {
        setSelectedDates(selectedDates.filter(d => d.toDateString() !== dateString));
      } else {
        setSelectedDates([...selectedDates, date]);
      }
    } else {
      setSelectedDate(date);
      fetchAvailabilityForDate(date);
      setError(null);
      setSuccess(null);
    }
  };

  // Quick date range selections
  const selectWeek = () => {
    const start = startOfWeek(selectedDate, { weekStartsOn: 1 }); // Monday
    const end = endOfWeek(selectedDate, { weekStartsOn: 1 });
    const weekDays = eachDayOfInterval({ start, end });
    setSelectedDates(weekDays);
    setBulkMode(true);
  };

  const selectNext7Days = () => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      days.push(addDays(new Date(), i));
    }
    setSelectedDates(days);
    setBulkMode(true);
  };

  // Handle full day unavailability toggle
  const handleFullDayToggle = () => {
    setIsFullDayUnavailable(!isFullDayUnavailable);
  };

  return (
    <div className="calendar-management">
      <div className="calendar-management-header">
        <h2>Availability Management</h2>
        <p>Manage your daily availability and time slots</p>

        {/* Global Controls */}
        <div className="global-controls">
          <button
            onClick={() => setShowGlobalSettings(!showGlobalSettings)}
            className="btn-secondary"
          >
            {showGlobalSettings ? 'Hide' : 'Show'} Global Settings
          </button>

          <button
            onClick={handleToggleGlobalAvailability}
            className={`btn ${globalSettings?.isGloballyUnavailable ? 'btn-success' : 'btn-danger'}`}
            disabled={loading}
          >
            {globalSettings?.isGloballyUnavailable ? 'Enable All Availability' : 'Disable All Availability'}
          </button>

          <button
            onClick={() => setBulkMode(!bulkMode)}
            className={`btn ${bulkMode ? 'btn-primary' : 'btn-secondary'}`}
          >
            {bulkMode ? 'Exit Bulk Mode' : 'Bulk Mode'}
          </button>
        </div>

        {/* Global Settings Panel */}
        {showGlobalSettings && globalSettings && (
          <div className="global-settings-panel">
            <h3>Global Settings</h3>
            <div className="settings-grid">
              <div>
                <strong>Business Hours:</strong> {globalSettings.businessHours.start} - {globalSettings.businessHours.end}
              </div>
              <div>
                <strong>Working Days:</strong> {globalSettings.workingDays.map(day =>
                  ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][day]
                ).join(', ')}
              </div>
              <div>
                <strong>Slot Duration:</strong> {globalSettings.slotDuration} minutes
              </div>
              <div>
                <strong>Global Status:</strong>
                <span className={globalSettings.isGloballyUnavailable ? 'status-unavailable' : 'status-available'}>
                  {globalSettings.isGloballyUnavailable ? 'Unavailable' : 'Available'}
                </span>
              </div>
            </div>

            {/* Bulk Operations */}
            <div className="bulk-operations">
              <h4>Bulk Operations</h4>
              <div className="bulk-buttons">
                <button
                  onClick={enableAllFutureDates}
                  className="btn-primary"
                  disabled={loading}
                >
                  Enable All Future Dates (Next 90 Days)
                </button>
              </div>
              <p className="help-text">
                This will set default availability for all dates in the next 90 days, making them bookable with standard time slots.
              </p>
            </div>
          </div>
        )}

        {/* Bulk Mode Controls */}
        {bulkMode && (
          <div className="bulk-controls">
            <h3>Bulk Operations</h3>
            <div className="bulk-buttons">
              <button onClick={selectNext7Days} className="btn-secondary">
                Select Next 7 Days
              </button>
              <button onClick={selectWeek} className="btn-secondary">
                Select This Week
              </button>
              <button
                onClick={() => setSelectedDates([])}
                className="btn-secondary"
                disabled={selectedDates.length === 0}
              >
                Clear Selection ({selectedDates.length})
              </button>
            </div>
          </div>
        )}
      </div>

      <div className="calendar-container">
        <div className="calendar-section">
          <Calendar
            onChange={(value: any, _event: React.MouseEvent<HTMLButtonElement>) => {
              if (value instanceof Date) {
                handleDateSelect(value);
              }
            }}
            value={selectedDate}
            minDate={new Date()}
            className="react-calendar"
            tileClassName={({ date }) => {
              if (bulkMode) {
                const isSelected = selectedDates.some(d => d.toDateString() === date.toDateString());
                return isSelected ? 'selected-bulk' : '';
              } else {
                return date.toDateString() === selectedDate.toDateString() ? 'selected-date' : '';
              }
            }}
          />
        </div>

        <div className="availability-section">
          {bulkMode ? (
            <h3>Bulk Edit - {selectedDates.length} dates selected</h3>
          ) : (
            <>
              <h3>Availability for {format(selectedDate, 'MMMM d, yyyy')}</h3>

              {/* Current availability status */}
              <div className="availability-status">
                {hasExistingAvailability ? (
                  <div className="status-info">
                    <span className="status-badge custom">Custom Availability Set</span>
                    {isFullDayUnavailable && (
                      <span className="status-badge unavailable">Full Day Unavailable</span>
                    )}
                    {reason && <p className="reason">Reason: {reason}</p>}

                    <div className="status-actions">
                      {isFullDayUnavailable ? (
                        <button
                          onClick={() => enableDay(selectedDate)}
                          className="btn-primary btn-small"
                          disabled={loading}
                        >
                          Enable Day
                        </button>
                      ) : null}
                      <button
                        onClick={() => deleteAvailability(selectedDate)}
                        className="btn-secondary btn-small"
                        disabled={loading}
                      >
                        Reset to Default
                      </button>
                    </div>
                  </div>
                ) : (
                  <div className="status-info">
                    <span className="status-badge default">Using Default Settings</span>
                    <p className="help-text">No custom availability set for this date</p>
                  </div>
                )}
              </div>
            </>
          )}

          {/* Status Messages */}
          {error && <div className="error-message">{error}</div>}
          {success && <div className="success-message">{success}</div>}

          {loading ? (
            <LoadingSpinner />
          ) : (
            <>
              <div className="full-day-control">
                <label>
                  <input
                    type="checkbox"
                    checked={isFullDayUnavailable}
                    onChange={handleFullDayToggle}
                  />
                  Mark {bulkMode ? 'Selected Days' : 'Full Day'} as Unavailable
                </label>
              </div>

              {isFullDayUnavailable && (
                <div className="reason-input">
                  <label>
                    Reason (optional):
                    <input
                      type="text"
                      value={reason}
                      onChange={(e) => setReason(e.target.value)}
                      placeholder="e.g., Holiday, Personal Day, etc."
                    />
                  </label>
                </div>
              )}

              {!isFullDayUnavailable && (
                <>
                  <div className="time-slot-controls">
                    <button
                      onClick={makeAllTimeSlotsAvailable}
                      className="btn-primary btn-small"
                      disabled={loading}
                    >
                      Make All Available
                    </button>
                    <button
                      onClick={() => {
                        if (timeSlots && timeSlots.length > 0) {
                          const allAvailable = timeSlots.map(slot => ({ ...slot, isAvailable: true }));
                          setTimeSlots(allAvailable);
                        }
                      }}
                      className="btn-secondary btn-small"
                      disabled={!timeSlots || timeSlots.length === 0}
                    >
                      Select All
                    </button>
                    <button
                      onClick={() => {
                        if (timeSlots && timeSlots.length > 0) {
                          const allUnavailable = timeSlots.map(slot => ({ ...slot, isAvailable: false }));
                          setTimeSlots(allUnavailable);
                        }
                      }}
                      className="btn-secondary btn-small"
                      disabled={!timeSlots || timeSlots.length === 0}
                    >
                      Deselect All
                    </button>
                  </div>

                  <div className="time-slots">
                    {timeSlots && timeSlots.length > 0 ? (
                      timeSlots.map((slot, index) => (
                        <div key={slot.time} className="time-slot">
                          <label>
                            <input
                              type="checkbox"
                              checked={slot.isAvailable}
                              onChange={() => handleTimeSlotToggle(index)}
                            />
                            {formatTo12Hour(slot.time)}
                          </label>
                        </div>
                      ))
                    ) : (
                      <div className="no-slots">
                        <p>No time slots available for this date</p>
                        {reason && <p className="reason-text">Reason: {reason}</p>}
                        {hasExistingAvailability && (
                          <button
                            onClick={() => enableDay(selectedDate)}
                            className="btn-primary btn-small"
                            disabled={loading}
                          >
                            Enable Day with Default Time Slots
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                </>
              )}

              <div className="save-button">
                <button
                  onClick={handleSave}
                  disabled={loading || (bulkMode && selectedDates.length === 0)}
                  className="btn-primary"
                >
                  {loading ? 'Saving...' : bulkMode ? `Save for ${selectedDates.length} dates` : 'Save Availability'}
                </button>

                {bulkMode && selectedDates.length === 0 && (
                  <p className="help-text">Select dates on the calendar to apply bulk changes</p>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
