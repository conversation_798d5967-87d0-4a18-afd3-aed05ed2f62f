import { API_CONFIG } from './config';

/**
 * Upload a single file to Cloudinary for service images
 * @param file - The file to upload
 * @param uploadType - Type of upload (serviceImage, etc.)
 * @returns Promise with upload response
 */
export const uploadFileToCloudinary = async (file: File, uploadType: string = 'serviceImage') => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const token = localStorage.getItem('authToken') || localStorage.getItem('token');

    // Remove /api/v2 from base URL and use /api/upload instead
    const baseUrl = API_CONFIG.BASE_URL.replace('/api/v2', '/api');
    const uploadUrl = `${baseUrl}/upload/cloudinary/single/${uploadType}`;

    console.log('🔄 Uploading file to:', uploadUrl);
    console.log('🔑 Token available:', !!token);

    const response = await fetch(uploadUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Upload failed:', response.status, response.statusText, errorText);
      throw new Error(`Upload failed: ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Upload successful:', result);
    return result;
  } catch (error) {
    console.error('❌ Error uploading file to Cloudinary:', error);
    throw error;
  }
};

/**
 * Upload multiple files to Cloudinary
 * @param files - Array of files to upload
 * @param uploadType - Type of upload (serviceImage, etc.)
 * @returns Promise with upload response
 */
export const uploadFilesToCloudinary = async (files: File[], uploadType: string = 'serviceImage') => {
  try {
    const formData = new FormData();
    files.forEach(file => formData.append('files', file));

    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    
    // Remove /api/v2 from base URL and use /api/upload instead
    const baseUrl = API_CONFIG.BASE_URL.replace('/api/v2', '/api');
    const response = await fetch(`${baseUrl}/upload/cloudinary/multiple/${uploadType}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error uploading files to Cloudinary:', error);
    throw error;
  }
};

/**
 * Delete an image from Cloudinary
 * @param imageUrl - URL of the image to delete
 * @returns Promise with deletion response
 */
export const deleteImageFromCloudinary = async (imageUrl: string) => {
  try {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    
    // Remove /api/v2 from base URL and use /api/upload instead
    const baseUrl = API_CONFIG.BASE_URL.replace('/api/v2', '/api');
    const response = await fetch(`${baseUrl}/upload/cloudinary/image`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ imageUrl })
    });

    if (!response.ok) {
      throw new Error(`Delete failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error deleting image from Cloudinary:', error);
    throw error;
  }
};

/**
 * Validate file before upload
 * @param file - File to validate
 * @param options - Validation options
 * @returns Validation result
 */
export const validateFile = (file: File, options: {
  maxSize?: number;
  allowedTypes?: string[];
} = {}) => {
  const {
    maxSize = 5 * 1024 * 1024, // 5MB default
    allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  } = options;

  const errors: string[] = [];

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`);
  }

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    errors.push(`File type must be one of: ${allowedTypes.join(', ')}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Upload service image using the service-specific endpoint
 * @param serviceId - ID of the service
 * @param file - Image file to upload
 * @returns Promise with upload response
 */
export const uploadServiceImage = async (serviceId: string, file: File) => {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    
    const response = await fetch(`${API_CONFIG.BASE_URL}/services/${serviceId}/upload-image`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error uploading service image:', error);
    throw error;
  }
};

/**
 * Delete service image using the service-specific endpoint
 * @param serviceId - ID of the service
 * @param imageUrl - URL of the image to delete
 * @returns Promise with deletion response
 */
export const deleteServiceImage = async (serviceId: string, imageUrl: string) => {
  try {
    const token = localStorage.getItem('authToken') || localStorage.getItem('token');
    
    const response = await fetch(`${API_CONFIG.BASE_URL}/services/${serviceId}/delete-image`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify({ imageUrl })
    });

    if (!response.ok) {
      throw new Error(`Delete failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error('Error deleting service image:', error);
    throw error;
  }
};
