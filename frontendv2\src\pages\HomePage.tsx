import { useState, useEffect } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import Header from '../components/Layout/Header'
import HeroSection from '../components/Home/HeroSection'
import AboutSection from '../components/Home/AboutSection'
import MicrolocksSection from '../components/Home/MicrolocksSection'
import PriceListSection from '../components/Home/PriceListSection'
import ConsultationSection from '../components/Home/ConsultationSection'
import PoliciesSection from '../components/Home/PoliciesSection'
import DisclaimerSection from '../components/Home/DisclaimerSection'
import { type User } from '../utils/api'
import { API_CONFIG } from '../utils/config'
import microlocImage from '../assets/microloc.jpg'

interface Review {
  _id: string;
  service?: {
    _id: string;
    name: string;
  };
  user?: {
    _id: string;
    firstName: string;
    lastName: string;
  };
  rating: number;
  title?: string;
  comment?: string;
  customerName?: string;
  status: 'pending' | 'approved' | 'rejected';
  isVerifiedPurchase: boolean;
  createdAt: string;
}
import { getMicrolocImageSources, createMicrolocImageErrorHandler } from '../utils/imageConstants'

// Robust Microloc Image Component with multiple fallbacks
const MicrolocImage: React.FC = () => {
  const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading')

  // Get image sources in order of preference
  const imageSources = getMicrolocImageSources(microlocImage)
  const [currentSrc, setCurrentSrc] = useState(imageSources[0])
  const [currentIndex, setCurrentIndex] = useState(0)

  const handleImageLoad = () => {
    console.log(`Microloc image loaded successfully from: ${currentSrc}`)
    setImageState('loaded')
  }

  const handleImageError = createMicrolocImageErrorHandler(
    imageSources,
    currentIndex,
    setCurrentIndex,
    setCurrentSrc,
    setImageState
  )

  if (imageState === 'error') {
    return (
      <div className="microloc-error-placeholder">
        <div style={{
          background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
          borderRadius: '12px',
          padding: '2rem',
          textAlign: 'center',
          color: '#666',
          border: '2px dashed #ddd'
        }}>
          <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>🖼️</div>
          <p style={{ margin: 0, fontSize: '0.9rem' }}>Microloc Image</p>
          <p style={{ margin: '0.25rem 0 0 0', fontSize: '0.75rem', opacity: 0.7 }}>
            Image temporarily unavailable
          </p>
        </div>
      </div>
    )
  }

  return (
    <img
      src={currentSrc}
      alt="Microlocs hairstyle example"
      className="microloc-landing-image"
      loading="lazy"
      onLoad={handleImageLoad}
      onError={handleImageError}
      style={{
        opacity: imageState === 'loaded' ? '1' : '0.7',
        transition: 'opacity 0.3s ease',
        filter: imageState === 'loading' ? 'blur(1px)' : 'none'
      }}
    />
  )
}

interface HomePageProps {
  currentUser: User | null;
  onLogout: () => void;
}

export default function HomePage({ currentUser, onLogout }: HomePageProps) {
  const navigate = useNavigate();
  const [showConsultationDetails, setShowConsultationDetails] = useState(false);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [reviewsLoading, setReviewsLoading] = useState(true);

  useEffect(() => {
    loadReviews();
  }, []);

  const loadReviews = async () => {
    try {
      setReviewsLoading(true);
      const response = await fetch(`${API_CONFIG.BASE_URL}/reviews?limit=3&status=approved`);
      const data = await response.json();

      if (data.success) {
        setReviews(data.data.reviews || []);
      }
    } catch (error) {
      console.error('Error loading reviews:', error);
      setReviews([]);
    } finally {
      setReviewsLoading(false);
    }
  };

  const handleBookService = (service: any) => {
    // Navigate to booking flow with service data
    const searchParams = new URLSearchParams({
      service: JSON.stringify(service)
    });
    navigate(`/booking/datetime?${searchParams.toString()}`);
  };

  // Navigation handlers are handled by the Header component now

  return (
    <div className="app">
      <Header
        currentUser={currentUser}
        onLogout={onLogout}
      />

      <main className="main-content">
        <HeroSection />
        
        <div className="info-sections">
          <AboutSection />
          <PoliciesSection />
          <DisclaimerSection />

          {/* Microloc Image Section */}
          <div className="microloc-image-section">
            <div className="microloc-image-container">
              <MicrolocImage />
            </div>
          </div>

          <MicrolocksSection />
          <PriceListSection onBookService={handleBookService} />

          {/* Reviews Section */}
          <div className="reviews-section">
            <div className="section-container">
              <div className="section-header">
                <h2 className="section-title">Client Reviews</h2>
                <p className="section-subtitle">
                  See what our clients are saying about their experience
                </p>
              </div>

              <div className="reviews-content">
                {reviewsLoading ? (
                  <div className="loading-container">
                    <p>Loading reviews...</p>
                  </div>
                ) : reviews.length === 0 ? (
                  <div className="empty-state">
                    <h3>No reviews yet</h3>
                    <p>Be the first to share your experience!</p>
                  </div>
                ) : (
                  <div className="reviews-grid">
                    {reviews.map((review) => (
                      <div key={review._id} className="review-card">
                        <div className="review-stars">
                          {'★'.repeat(review.rating)}{'☆'.repeat(5 - review.rating)}
                        </div>
                        <p className="review-text">
                          {review.comment ? `"${review.comment}"` : `"${review.title || 'Great service!'}"`}
                        </p>
                        <p className="review-author">
                          - {review.user ? `${review.user.firstName} ${review.user.lastName.charAt(0)}.` : review.customerName || 'Anonymous'}
                        </p>
                      </div>
                    ))}
                  </div>
                )}

                <div className="reviews-actions">
                  <Link
                    to="/reviews?tab=create"
                    className="btn btn-primary"
                  >
                    Share Your Experience
                  </Link>
                  <Link
                    to="/reviews"
                    className="btn btn-outline"
                  >
                    View All Reviews
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>

        <ConsultationSection
          onBookService={handleBookService}
          showDetails={showConsultationDetails}
          onToggleDetails={() => setShowConsultationDetails(!showConsultationDetails)}
        />
      </main>
    </div>
  )
}
