import { useNavigate, useSearchParams } from 'react-router-dom'
import DateTimeSelection from '../DateTimeSelection'
import { addOnServices, getAddOnsForService, type AddOnService } from '../../config/services'
import { serviceAPI } from '../../utils/serviceAPI'
import { useEffect, useState } from 'react'

export default function DateTimeStep() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [selectedService, setSelectedService] = useState<any>(null);
  const [selectedAddOns, setSelectedAddOns] = useState<AddOnService[]>([]);

  useEffect(() => {
    const loadServiceData = async () => {
      // Get service from URL params or sessionStorage
      const serviceParam = searchParams.get('service');
      const addonsParam = searchParams.get('addons');

      let service = null;
      let addons: AddOnService[] = [];

      if (serviceParam) {
        try {
          service = JSON.parse(serviceParam);
          console.log('🔍 Service from URL params:', service);
        } catch (error) {
          console.error('Error parsing service from URL:', error);
        }
      }

      // Fallback to sessionStorage if URL params don't have service
      if (!service) {
        try {
          const sessionService = sessionStorage.getItem('selectedService');
          if (sessionService) {
            service = JSON.parse(sessionService);
            console.log('🔍 Service from sessionStorage:', service);
          }
        } catch (error) {
          console.error('Error parsing service from sessionStorage:', error);
        }
      }

      // If service exists but doesn't have images, try to fetch complete data
      if (service && (!service.image && !service.images)) {
        try {
          console.log('🔍 Service missing images, fetching complete data for:', service.id);
          const completeServiceResponse = await serviceAPI.getService(service.id);
          if (completeServiceResponse.success) {
            service = completeServiceResponse.data;
            console.log('🔍 Fetched complete service with images:', service);
          }
        } catch (error) {
          console.error('Failed to fetch complete service data:', error);

          // Fallback: Add known image data for LOCS STYLING service
          if (service.name === 'LOCS STYLING') {
            service = {
              ...service,
              image: "https://res.cloudinary.com/djeddsyoq/image/upload/v1755717788/microlocs/services/gukxkcdjduaateaks97v.png",
              images: ["https://res.cloudinary.com/djeddsyoq/image/upload/v1755717788/microlocs/services/gukxkcdjduaateaks97v.png"]
            };
            console.log('🔍 Added fallback images to LOCS STYLING service:', service);
          }
        }
      }

      if (addonsParam) {
        try {
          addons = JSON.parse(addonsParam);
        } catch (error) {
          console.error('Error parsing addons:', error);
        }
      }

      setSelectedService(service);
      setSelectedAddOns(addons);
    };

    loadServiceData();
  }, [searchParams]);


  // Get add-ons based on service type (exclude for consultation)
  const availableAddOns = selectedService ? getAddOnsForService(selectedService.id) : addOnServices;

  const booking = {
    selectedService,
    selectedAddOns,
    selectedDate: '',
    selectedTime: '',
    step: 'datetime' as const
  };

  const handleBack = () => {
    navigate('/');
  };

  const handleSelect = (date: string, time: string) => {
    const params = new URLSearchParams();
    params.set('service', JSON.stringify(selectedService));
    if (selectedAddOns.length > 0) {
      params.set('addons', JSON.stringify(selectedAddOns));
    }
    params.set('date', date);
    params.set('time', time);
    
    navigate(`/booking/details?${params.toString()}`);
  };

  const handleAddOnToggle = (addOn: AddOnService) => {
    const isSelected = selectedAddOns.find(item => item.id === addOn.id);
    let newAddOns;

    if (isSelected) {
      newAddOns = selectedAddOns.filter(item => item.id !== addOn.id);
    } else {
      newAddOns = [...selectedAddOns, addOn];
    }

    // Update URL with new addons
    const params = new URLSearchParams();
    params.set('service', JSON.stringify(selectedService));
    if (newAddOns.length > 0) {
      params.set('addons', JSON.stringify(newAddOns));
    }
    
    navigate(`/booking/datetime?${params.toString()}`, { replace: true });
  };

  if (!selectedService) {
    navigate('/');
    return null;
  }

  return (
    <DateTimeSelection
      booking={booking}
      onBack={handleBack}
      onSelect={handleSelect}
      addOnServices={availableAddOns}
      onAddOnToggle={handleAddOnToggle}
    />
  );
}
